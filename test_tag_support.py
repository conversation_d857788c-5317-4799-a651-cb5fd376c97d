#!/usr/bin/env python3
"""
测试LaTeX \tag命令的支持情况
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_tag_commands():
    """测试不同的\tag命令"""
    print("=== 测试LaTeX \\tag命令支持 ===")
    
    # 不同的\tag使用方式
    tag_test_cases = [
        {
            "name": "基本tag命令",
            "latex": r"\begin{equation} E = mc^2 \tag{1} \end{equation}",
            "description": "最基本的tag用法"
        },
        {
            "name": "gather环境中的tag",
            "latex": r"\begin{gather} a + b = c \tag{2.1} \\ x + y = z \tag{2.2} \end{gather}",
            "description": "在gather环境中使用tag"
        },
        {
            "name": "align环境中的tag",
            "latex": r"\begin{align} f(x) &= ax + b \tag{3.1} \\ g(x) &= cx + d \tag{3.2} \end{align}",
            "description": "在align环境中使用tag"
        },
        {
            "name": "自定义tag内容",
            "latex": r"\begin{equation} \int_0^1 f(x) dx = F(1) - F(0) \tag{*} \end{equation}",
            "description": "使用自定义符号作为tag"
        },
        {
            "name": "中文tag",
            "latex": r"\begin{equation} \sum_{i=1}^n x_i = S \tag{公式1} \end{equation}",
            "description": "使用中文作为tag"
        },
        {
            "name": "无编号环境中的tag",
            "latex": r"\begin{equation*} \lim_{x \to 0} \frac{\sin x}{x} = 1 \tag{极限} \end{equation*}",
            "description": "在无编号环境中使用tag"
        }
    ]
    
    try:
        from render_image import render_latex_to_image
        
        results = {}
        
        for i, test_case in enumerate(tag_test_cases, 1):
            name = test_case["name"]
            latex = test_case["latex"]
            description = test_case["description"]
            
            print(f"\n测试 {i}: {name}")
            print(f"描述: {description}")
            print(f"LaTeX: {latex}")
            
            output_file = f"tag_test_{i}_{name.replace(' ', '_')}.png"
            
            try:
                success = render_latex_to_image(latex, output_file)
                
                if success:
                    print(f"  ✅ 渲染成功: {output_file}")
                    results[name] = "成功"
                else:
                    print(f"  ❌ 渲染失败")
                    results[name] = "失败"
                    
            except Exception as e:
                print(f"  ❌ 渲染出错: {str(e)[:100]}...")
                results[name] = f"错误: {str(e)[:50]}..."
        
        return results
        
    except ImportError as e:
        print(f"❌ 无法导入render_image模块: {e}")
        return {}

def analyze_tag_issues():
    """分析\tag命令可能的问题"""
    print("\n=== \\tag命令问题分析 ===")
    
    print("\n🔍 可能的问题原因:")
    print("1. **环境兼容性**: \\tag命令需要特定的数学环境")
    print("2. **宏包支持**: 可能需要额外的LaTeX宏包")
    print("3. **文档类限制**: standalone文档类可能对某些命令有限制")
    print("4. **编译引擎**: xelatex对某些命令的支持可能不同")
    
    print("\n📋 \\tag命令的正确用法:")
    print("• equation环境: \\begin{equation} ... \\tag{label} \\end{equation}")
    print("• gather环境: \\begin{gather} ... \\tag{label} \\\\ ... \\end{gather}")
    print("• align环境: \\begin{align} ... \\tag{label} \\\\ ... \\end{align}")
    print("• 无编号环境: \\begin{equation*} ... \\tag{label} \\end{equation*}")
    
    print("\n🔧 可能的解决方案:")
    print("1. 添加必要的宏包 (如 amsmath, mathtools)")
    print("2. 修改文档类或添加选项")
    print("3. 调整LaTeX模板以更好支持标签")
    print("4. 使用替代的标签方法")

def suggest_fixes():
    """建议修复方案"""
    print("\n=== 修复建议 ===")
    
    print("\n💡 LaTeX模板改进建议:")
    
    print("\n1. **确保宏包完整性**:")
    print("   当前模板已包含: amsmath, amsfonts, amssymb, mathtools")
    print("   这些宏包应该足够支持\\tag命令")
    
    print("\n2. **检查文档类兼容性**:")
    print("   standalone文档类通常支持\\tag命令")
    print("   但可能需要特定的选项或配置")
    
    print("\n3. **环境处理优化**:")
    print("   当前使用gather*环境，可能需要调整为支持\\tag的环境")
    
    print("\n4. **替代方案**:")
    print("   如果\\tag不工作，可以考虑:")
    print("   • 使用\\text{(标签)}在公式右侧")
    print("   • 使用\\qquad\\text{(标签)}添加间距")
    print("   • 手动添加编号显示")

def main():
    """主函数"""
    print("LaTeX \\tag命令支持测试")
    print("=" * 50)
    
    print("\n🎯 测试目标:")
    print("  检查当前渲染脚本对\\tag命令的支持情况")
    print("  识别可能的问题并提供解决方案")
    
    # 分析问题
    analyze_tag_issues()
    
    # 建议修复方案
    suggest_fixes()
    
    # 运行测试
    print("\n🧪 开始测试...")
    results = test_tag_commands()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("🎯 测试结果总结")
    print("=" * 50)
    
    if results:
        success_count = sum(1 for result in results.values() if result == "成功")
        total_count = len(results)
        
        print(f"\n📊 测试统计:")
        print(f"  总测试数: {total_count}")
        print(f"  成功数: {success_count}")
        print(f"  失败数: {total_count - success_count}")
        print(f"  成功率: {success_count/total_count*100:.1f}%")
        
        print(f"\n📋 详细结果:")
        for name, result in results.items():
            status = "✅" if result == "成功" else "❌"
            print(f"  {status} {name}: {result}")
        
        if success_count == total_count:
            print(f"\n🎉 所有\\tag命令测试通过！")
            print(f"当前渲染脚本完全支持\\tag命令")
        elif success_count > 0:
            print(f"\n⚠️  部分\\tag命令支持有问题")
            print(f"建议检查失败的测试用例")
        else:
            print(f"\n❌ \\tag命令完全不支持")
            print(f"需要修改LaTeX模板或添加必要支持")
    else:
        print(f"\n❌ 无法运行测试，请检查环境配置")
    
    print(f"\n💡 下一步建议:")
    if results and success_count < len(results):
        print(f"  1. 检查失败的测试用例的具体错误信息")
        print(f"  2. 考虑修改LaTeX模板以更好支持\\tag")
        print(f"  3. 测试替代的标签方法")
    else:
        print(f"  1. 如果测试通过，\\tag命令应该正常工作")
        print(f"  2. 如果实际使用中仍有问题，请提供具体的LaTeX代码")

if __name__ == "__main__":
    main()
