This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.7.15)  25 JUL 2025 11:35
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./manual_tag_verification.tex
(manual_tag_verification.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(D:\softwares\MiKTeX\tex/latex/standalone\standalone.cls
Document Class: standalone 2022/10/10 v1.3b Class to compile TeX sub-files stan
dalone
(D:\softwares\MiKTeX\tex/latex/tools\shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
)
(D:\softwares\MiKTeX\tex/generic/iftex\ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.

(D:\softwares\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
(D:\softwares\MiKTeX\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(D:\softwares\MiKTeX\tex/generic/xkeyval\xkeyval.tex
(D:\softwares\MiKTeX\tex/generic/xkeyval\xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18

(D:\softwares\MiKTeX\tex/generic/xkeyval\keyval.tex))
\XKV@depth=\count183
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\sa@internal=\count184
\c@sapage=\count185

(D:\softwares\MiKTeX\tex/latex/standalone\standalone.cfg
File: standalone.cfg 2022/10/10 v1.3b Default configuration file for 'standalon
e' class
)
(D:\softwares\MiKTeX\tex/latex/base\article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(D:\softwares\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count186
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@subparagraph=\count191
\c@figure=\count192
\c@table=\count193
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
\sa@box=\box51
)
(D:\softwares\MiKTeX\tex/latex/ctex\ctex.sty
(D:\softwares\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-01-04 L3 programming layer (loader) 

(D:\softwares\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count194
\l__pdf_internal_box=\box52
\g__pdf_backend_object_int=\count195
\g__pdf_backend_annotation_int=\count196
\g__pdf_backend_link_int=\count197
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)

(D:\softwares\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(D:\softwares\MiKTeX\tex/latex/ctex\ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(D:\softwares\MiKTeX\tex/latex/base\fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(D:\softwares\MiKTeX\tex/latex/base\ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count198
\l__ctex_tmp_box=\box53
\l__ctex_tmp_dim=\dimen141
\g__ctex_section_depth_int=\count199
\g__ctex_font_size_int=\count266

(D:\softwares\MiKTeX\tex/latex/ctex/config\ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(D:\softwares\MiKTeX\tex/latex/ctex/engine\ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)

(D:\softwares\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(D:\softwares\MiKTeX\tex/latex/l3packages/xtemplate\xtemplate.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen142
\l__xtemplate_tmp_int=\count267
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip50
)
\l__xeCJK_tmp_int=\count268
\l__xeCJK_tmp_box=\box54
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count269
\l__xeCJK_begin_int=\count270
\l__xeCJK_end_int=\count271
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count272
\g__xeCJK_node_int=\count273
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box55
\l__xeCJK_last_penalty_int=\count274
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count275

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count276
\l__xeCJK_fam_int=\count277
\g__xeCJK_fam_allocation_int=\count278
\l__xeCJK_verb_case_int=\count279
\l__xeCJK_verb_exspace_skip=\skip57

(D:\softwares\MiKTeX\tex/latex/fontspec\fontspec.sty
(D:\softwares\MiKTeX\tex/latex/l3packages/xparse\xparse.sty
Package: xparse 2023-10-10 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX

(D:\softwares\MiKTeX\tex/latex/fontspec\fontspec-xetex.sty
Package: fontspec-xetex 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count280
\l__fontspec_language_int=\count281
\l__fontspec_strnum_int=\count282
\l__fontspec_tmp_int=\count283
\l__fontspec_tmpa_int=\count284
\l__fontspec_tmpb_int=\count285
\l__fontspec_tmpc_int=\count286
\l__fontspec_em_int=\count287
\l__fontspec_emdef_int=\count288
\l__fontspec_strong_int=\count289
\l__fontspec_strongdef_int=\count290
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172

(D:\softwares\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(D:\softwares\MiKTeX\tex/latex/fontspec\fontspec.cfg)))
(D:\softwares\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174

(D:\softwares\MiKTeX\tex/latex/zhnumber\zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count291
\l__zhnum_tmp_int=\count292

(D:\softwares\MiKTeX\tex/latex/zhnumber\zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
(D:\softwares\MiKTeX\tex/latex/ctex/scheme\ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CT
EX)

(D:\softwares\MiKTeX\tex/latex/ctex/config\ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
))
(D:\softwares\MiKTeX\tex/latex/tools\indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(D:\softwares\MiKTeX\tex/latex/ctex\ctex-c5size.clo
File: ctex-c5size.clo 2022/07/14 v2.5.10 c5size option (CTEX)
)
(D:\softwares\MiKTeX\tex/latex/ctex/fontset\ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTE
X)

Package fontspec Info: Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

))
(D:\softwares\MiKTeX\tex/latex/ctex/config\ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
)
(D:\softwares\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip59

For additional information on amsmath, use the `?' option.
(D:\softwares\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(D:\softwares\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen175
))
(D:\softwares\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen176
)
(D:\softwares\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count293
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count294
\leftroot@=\count295
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count296
\DOTSCASE@=\count297
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box56
\strutbox@=\box57
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen177
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count298
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count299
\dotsspace@=\muskip17
\c@parentequation=\count300
\dspbrk@lvl=\count301
\tag@help=\toks20
\row@=\count302
\column@=\count303
\maxfields@=\count304
\andhelp@=\toks21
\eqnshift@=\dimen178
\alignsep@=\dimen179
\tagshift@=\dimen180
\tagwidth@=\dimen181
\totwidth@=\dimen182
\lineht@=\dimen183
\@envbody=\toks22
\multlinegap=\skip60
\multlinetaggap=\skip61
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(D:\softwares\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(D:\softwares\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(D:\softwares\MiKTeX\tex/latex/mathtools\mathtools.sty
Package: mathtools 2024/10/04 v1.31 mathematical typesetting tools

(D:\softwares\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count305
\calc@Bcount=\count306
\calc@Adimen=\dimen184
\calc@Bdimen=\dimen185
\calc@Askip=\skip62
\calc@Bskip=\skip63
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count307
\calc@Cskip=\skip64
)
(D:\softwares\MiKTeX\tex/latex/mathtools\mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count308
\l_MT_multwidth_dim=\dimen186
\origjot=\skip65
\l_MT_shortvdotswithinadjustabove_dim=\dimen187
\l_MT_shortvdotswithinadjustbelow_dim=\dimen188
\l_MT_above_intertext_sep=\dimen189
\l_MT_below_intertext_sep=\dimen190
\l_MT_above_shortintertext_sep=\dimen191
\l_MT_below_shortintertext_sep=\dimen192
\xmathstrut@box=\box58
\xmathstrut@dim=\dimen193
)

LaTeX Warning: Unused global option(s):
    [24pt].

No file manual_tag_verification.aux.
\openout1 = `manual_tag_verification.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 8.
LaTeX Font Info:    Redeclaring math accent \acute on input line 8.
LaTeX Font Info:    Redeclaring math accent \grave on input line 8.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 8.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 8.
LaTeX Font Info:    Redeclaring math accent \bar on input line 8.
LaTeX Font Info:    Redeclaring math accent \breve on input line 8.
LaTeX Font Info:    Redeclaring math accent \check on input line 8.
LaTeX Font Info:    Redeclaring math accent \hat on input line 8.
LaTeX Font Info:    Redeclaring math accent \dot on input line 8.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 8.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 8.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 8.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 8.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 8.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 8.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 8.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 8.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 8.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 8.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 8.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 8.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 8.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 8.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 8.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 8.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 8.
(D:\softwares\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(D:\softwares\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(D:\softwares\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(D:\softwares\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.

(D:\softwares\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen194
\Gin@req@width=\dimen195
)
LaTeX Font Info:    Calculating math sizes for size <24> on input line 10.
LaTeX Font Info:    Trying to load font information for U+msa on input line 10.


(D:\softwares\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 10.


(D:\softwares\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
! Missing \endgroup inserted.
<inserted text> 
                \endgroup 
l.12 \end{gather}
                 
? ＀＀
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
1 or ... or 9 to ignore the next 1 to 9 tokens of input,
H for help, X to quit.
? 
! Interruption.
<to be read again> 
                   \halign 
l.12 \end{gather}
                 
? python simple_tag_test.py
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
H for help, X to quit.
? 
! Missing \endgroup inserted.
<inserted text> 
                \endgroup 
l.12 \end{gather}
                 
? 
! Missing } inserted.
<inserted text> 
                }
l.12 \end{gather}
                 
? ＀
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
1 or ... or 9 to ignore the next 1 to 9 tokens of input,
H for help, X to quit.
? 
! Interruption.
<to be read again> 
                   \halign 
l.12 \end{gather}
                 
? xelatex minimal_tag_test.tex
 
Here is how much of TeX's memory you used:
 8932 strings out of 409617
 224882 string characters out of 5779088
 1918191 words of memory out of 5000000
 30927 multiletter control sequences out of 15000+600000
 564475 words of font info for 72 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,7n,97p,384b,271s stack positions out of 10000i,1000n,20000p,200000b,200000s
No pages of output.
