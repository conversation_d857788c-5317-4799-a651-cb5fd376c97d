#!/usr/bin/env python3
"""
测试保存对比图像功能
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_save_functionality():
    """测试保存功能"""
    print("=== 测试保存对比图像功能 ===")
    
    # 创建测试图片
    test_image_path = "test_save_image.png"
    if not Path(test_image_path).exists():
        import matplotlib.pyplot as plt
        
        fig, ax = plt.subplots(figsize=(4, 2))
        ax.text(0.5, 0.5, '保存功能测试图片\nSave Function Test', 
                ha='center', va='center', fontsize=16)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        plt.savefig(test_image_path, bbox_inches='tight', dpi=150)
        plt.close()
        print(f"✅ 创建测试图片: {test_image_path}")
    
    # 测试LaTeX字符串
    test_latex = r"\frac{a+b}{c+d}"
    
    print(f"\n🧪 测试设置:")
    print(f"  测试图片: {test_image_path}")
    print(f"  测试LaTeX: {test_latex}")
    print(f"  保存文件夹: output/")
    
    print(f"\n📋 测试说明:")
    print(f"  程序将显示对比窗口")
    print(f"  请按 'S' 键测试保存功能")
    print(f"  按空格键继续，按ESC键退出")
    
    try:
        from render_image import compare_image_with_rendered_latex
        
        print(f"\n🚀 启动测试...")
        action = compare_image_with_rendered_latex(test_image_path, test_latex)
        
        print(f"\n📊 测试结果:")
        print(f"  用户操作: {action}")
        
        # 检查output文件夹
        output_dir = Path("output")
        if output_dir.exists():
            saved_files = list(output_dir.glob("*.png"))
            if saved_files:
                print(f"  ✅ 发现保存的文件:")
                for file in saved_files:
                    print(f"    📁 {file}")
            else:
                print(f"  ⚠️  output文件夹存在但没有找到保存的文件")
        else:
            print(f"  ⚠️  output文件夹不存在")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        if Path(test_image_path).exists():
            os.remove(test_image_path)
            print(f"🧹 清理测试文件: {test_image_path}")

def test_save_function_directly():
    """直接测试保存函数"""
    print("\n=== 直接测试保存函数 ===")
    
    try:
        import matplotlib.pyplot as plt
        from render_image import save_comparison_image
        
        # 创建一个简单的figure用于测试
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(6, 8))
        
        ax1.text(0.5, 0.5, '测试原始图像\nTest Original Image', 
                ha='center', va='center', fontsize=14)
        ax1.set_title('原始图像')
        ax1.axis('off')
        
        ax2.text(0.5, 0.5, '测试渲染结果\nTest Rendered Result', 
                ha='center', va='center', fontsize=14)
        ax2.set_title('渲染结果')
        ax2.axis('off')
        
        fig.suptitle("测试对比图像", fontsize=16)
        
        # 测试保存功能
        test_latex = r"\frac{x^2+1}{x-1}"
        saved_path = save_comparison_image(fig, "test_image.png", test_latex)
        
        plt.close(fig)
        
        if saved_path:
            print(f"✅ 保存函数测试成功")
            print(f"📁 保存路径: {saved_path}")
            
            # 验证文件是否存在
            if Path(saved_path).exists():
                file_size = Path(saved_path).stat().st_size
                print(f"📊 文件大小: {file_size} 字节")
                return True
            else:
                print(f"❌ 保存的文件不存在")
                return False
        else:
            print(f"❌ 保存函数返回None")
            return False
            
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("保存对比图像功能测试")
    print("=" * 50)
    
    print("\n🎯 新功能说明:")
    print("  按键: S键")
    print("  功能: 保存当前对比图像到output文件夹")
    print("  格式: PNG格式，300DPI高质量")
    print("  命名: 原图名_时间戳_comparison.png")
    
    print("\n🔧 功能特点:")
    print("  ✅ 自动创建output文件夹")
    print("  ✅ 时间戳命名避免覆盖")
    print("  ✅ 安全文件名处理")
    print("  ✅ 高质量图像保存")
    print("  ✅ 错误处理和用户反馈")
    
    # 先测试保存函数
    print("\n🧪 开始测试...")
    direct_test_ok = test_save_function_directly()
    
    if direct_test_ok:
        print("\n✅ 保存函数基础测试通过")
        
        # 询问是否进行交互测试
        try:
            choice = input("\n是否进行交互测试？(y/n): ").strip().lower()
            if choice == 'y':
                interactive_test_ok = test_save_functionality()
                
                if interactive_test_ok:
                    print("\n🎉 所有测试通过！")
                else:
                    print("\n⚠️  交互测试有问题")
            else:
                print("\n跳过交互测试")
        except KeyboardInterrupt:
            print("\n用户中断测试")
    else:
        print("\n❌ 保存函数基础测试失败")
    
    print("\n💡 使用说明:")
    print("  现在您可以在主程序中使用保存功能:")
    print("  1. 运行: python msr/tools/render_image.py")
    print("  2. 查看对比图像")
    print("  3. 按 'S' 键保存当前对比图像")
    print("  4. 图像将保存到 output/ 文件夹")
    
    print("\n📁 保存的文件包含:")
    print("  • 原始图像")
    print("  • 渲染结果")
    print("  • LaTeX字符串显示")
    print("  • 完整的对比布局")

if __name__ == "__main__":
    main()
