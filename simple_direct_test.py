#!/usr/bin/env python3
"""
简单直接测试\tag命令
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def direct_test():
    """直接测试"""
    try:
        from render_image import render_latex_to_image
        
        test_latex = "a+b=c \\tag{2}"
        output_file = "direct_test_result.png"
        
        print(f"直接测试: {test_latex}")
        
        success = render_latex_to_image(test_latex, output_file)
        
        if success:
            print(f"✅ 成功: {output_file}")
        else:
            print("❌ 失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

if __name__ == "__main__":
    direct_test()
