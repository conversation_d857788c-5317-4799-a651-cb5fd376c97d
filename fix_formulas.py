#!/usr/bin/env python3
r"""
Fix broken LaTeX formulas in the prediction file.
Each formula should start with \[ and end with \].
"""

def fix_formulas(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.splitlines()
    print(f"Read {len(lines)} lines from input file")
    
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Skip empty lines
        if not line.strip():
            i += 1
            continue
        
        # Extract line number and content
        # Format: "     1→content"
        arrow_pos = line.find('→')
        if arrow_pos == -1:
            # If no arrow found, this might be a continuation line
            print(f"Skipping line without arrow: {line[:50]}...")
            i += 1
            continue
            
        line_num = line[:arrow_pos].strip()
        content = line[arrow_pos+1:]
        
        print(f"Processing line {line_num}: {content[:50]}...")
        
        # Check if this line starts with \[ but doesn't end with \]
        if content.startswith('\\[') and not content.endswith('\\]'):
            print(f"Found broken formula starting at line {line_num}")
            # This is a broken formula, need to find the end
            formula_parts = [content]
            j = i + 1
            
            # Look for the closing \]
            while j < len(lines):
                if j >= len(lines):
                    break
                    
                next_line = lines[j]
                if not next_line.strip():
                    j += 1
                    continue
                    
                next_arrow_pos = next_line.find('→')
                if next_arrow_pos == -1:
                    # This might be a continuation line without line number
                    formula_parts.append(next_line.strip())
                    j += 1
                    continue
                    
                next_content = next_line[next_arrow_pos+1:]
                formula_parts.append(next_content)
                
                # If this line ends with \], we found the end
                if next_content.strip().endswith('\\]'):
                    next_line_num = next_line[:next_arrow_pos].strip()
                    print(f"Found end at line {next_line_num}")
                    break
                    
                j += 1
            
            # Join all parts with a space
            complete_formula = ' '.join(formula_parts)
            fixed_lines.append(f"{line_num}→{complete_formula}")
            print(f"Fixed formula: {complete_formula[:100]}...")
            
            # Skip all the lines we just processed
            i = j + 1
        else:
            # This line is fine as is
            fixed_lines.append(f"{line_num}→{content}")
            i += 1
    
    print(f"Generated {len(fixed_lines)} fixed lines")
    
    # Write the fixed content
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in fixed_lines:
            f.write(line + '\n')

if __name__ == "__main__":
    input_file = "msr/pred_texteller_3.txt"
    output_file = "msr/pred_texteller_3_fixed.txt"
    
    fix_formulas(input_file, output_file)
    print(f"Fixed formulas saved to {output_file}")