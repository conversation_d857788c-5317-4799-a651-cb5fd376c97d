#!/usr/bin/env python3
"""
演示保存对比图像功能
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def create_demo_files():
    """创建演示文件"""
    print("=== 创建演示文件 ===")
    
    # 创建演示图片
    demo_image_path = "demo_original.png"
    
    if not Path(demo_image_path).exists():
        import matplotlib.pyplot as plt
        
        fig, ax = plt.subplots(figsize=(6, 3))
        ax.text(0.5, 0.5, '演示原始图片\n(Demo Original Image)\n包含数学公式', 
                ha='center', va='center', fontsize=16, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        plt.savefig(demo_image_path, bbox_inches='tight', dpi=150)
        plt.close()
        print(f"✅ 创建演示图片: {demo_image_path}")
    
    return demo_image_path

def demo_save_functionality():
    """演示保存功能"""
    print("\n=== 演示保存功能 ===")
    
    # 创建演示文件
    demo_image = create_demo_files()
    
    # 演示用的LaTeX公式
    demo_latex = r"\frac{x^2+2x+1}{x^2-1} = \frac{(x+1)^2}{(x+1)(x-1)} = \frac{x+1}{x-1}"
    
    print(f"\n📋 演示设置:")
    print(f"  原始图片: {demo_image}")
    print(f"  LaTeX公式: {demo_latex}")
    print(f"  保存位置: output/ 文件夹")
    
    print(f"\n🎮 操作说明:")
    print(f"  1. 程序将显示对比窗口")
    print(f"  2. 按 'S' 键保存当前对比图像")
    print(f"  3. 按空格键继续，按ESC键退出")
    print(f"  4. 保存的文件将显示在控制台")
    
    try:
        from render_image import compare_image_with_rendered_latex
        
        print(f"\n🚀 启动演示...")
        action = compare_image_with_rendered_latex(demo_image, demo_latex)
        
        print(f"\n📊 演示结果:")
        print(f"  用户操作: {action}")
        
        # 检查保存的文件
        output_dir = Path("output")
        if output_dir.exists():
            saved_files = list(output_dir.glob("*.png"))
            if saved_files:
                print(f"\n✅ 保存成功！发现以下文件:")
                for file in saved_files:
                    file_size = file.stat().st_size
                    print(f"  📁 {file.name}")
                    print(f"     大小: {file_size:,} 字节")
                    print(f"     路径: {file}")
            else:
                print(f"\n⚠️  output文件夹存在但没有找到保存的文件")
                print(f"     可能用户没有按S键保存")
        else:
            print(f"\n⚠️  output文件夹不存在")
            print(f"     可能用户没有按S键保存")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        return False
    
    finally:
        # 清理演示文件
        if Path(demo_image).exists():
            os.remove(demo_image)
            print(f"\n🧹 清理演示文件: {demo_image}")

def show_save_feature_info():
    """显示保存功能信息"""
    print("保存对比图像功能说明")
    print("=" * 50)
    
    print("\n🎯 功能概述:")
    print("  新增了按 'S' 键保存当前对比图像的功能")
    print("  保存的图像包含完整的对比布局")
    
    print("\n🔧 技术特点:")
    print("  ✅ 自动创建 output/ 文件夹")
    print("  ✅ 智能文件命名：原图名_时间戳_comparison.png")
    print("  ✅ 高质量保存：300DPI PNG格式")
    print("  ✅ 安全文件名处理：移除特殊字符")
    print("  ✅ 错误处理：保存失败时显示错误信息")
    print("  ✅ 用户反馈：保存成功时显示路径和信息")
    
    print("\n🎮 使用方法:")
    print("  1. 运行主程序：python msr/tools/render_image.py")
    print("  2. 查看对比图像")
    print("  3. 按 'S' 键保存当前图像")
    print("  4. 控制台会显示保存信息")
    print("  5. 继续使用空格键或ESC键")
    
    print("\n📁 保存的文件包含:")
    print("  • 原始图像（上方）")
    print("  • 渲染结果（中间）")
    print("  • LaTeX字符串（下方）")
    print("  • 完整的标题和布局")
    print("  • 操作提示信息")
    
    print("\n📝 文件命名规则:")
    print("  格式：{原图名}_{时间戳}_comparison.png")
    print("  示例：formula_001_20250724_115444_comparison.png")
    print("  说明：时间戳确保文件不会被覆盖")
    
    print("\n⚙️  按键说明:")
    print("  • 空格键：进入下一个LaTeX字符串")
    print("  • ESC键：退出程序")
    print("  • S键：保存当前对比图像（新功能）")

def main():
    """主函数"""
    show_save_feature_info()
    
    print("\n🧪 是否要进行功能演示？")
    print("注意：这将打开matplotlib窗口进行交互演示")
    
    try:
        choice = input("\n输入 'y' 开始演示，其他键跳过: ").strip().lower()
        
        if choice == 'y':
            success = demo_save_functionality()
            
            if success:
                print("\n🎉 演示完成！")
                print("\n💡 现在您可以在主程序中使用保存功能了")
            else:
                print("\n⚠️  演示过程中出现问题")
        else:
            print("\n跳过演示")
    except KeyboardInterrupt:
        print("\n用户中断演示")
    
    print("\n✨ 保存功能已成功添加到您的LaTeX渲染对比程序中！")

if __name__ == "__main__":
    main()
