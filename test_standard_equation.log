This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.7.15)  25 JUL 2025 11:56
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./test_standard_equation.tex
(test_standard_equation.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(D:\softwares\MiKTeX\tex/latex/standalone\standalone.cls
Document Class: standalone 2022/10/10 v1.3b Class to compile TeX sub-files stan
dalone
(D:\softwares\MiKTeX\tex/latex/tools\shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
)
(D:\softwares\MiKTeX\tex/generic/iftex\ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.

(D:\softwares\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
(D:\softwares\MiKTeX\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(D:\softwares\MiKTeX\tex/generic/xkeyval\xkeyval.tex
(D:\softwares\MiKTeX\tex/generic/xkeyval\xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18

(D:\softwares\MiKTeX\tex/generic/xkeyval\keyval.tex))
\XKV@depth=\count183
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\sa@internal=\count184
\c@sapage=\count185

(D:\softwares\MiKTeX\tex/latex/standalone\standalone.cfg
File: standalone.cfg 2022/10/10 v1.3b Default configuration file for 'standalon
e' class
)
(D:\softwares\MiKTeX\tex/latex/base\article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(D:\softwares\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count186
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@subparagraph=\count191
\c@figure=\count192
\c@table=\count193
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
\sa@box=\box51
)
(D:\softwares\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(D:\softwares\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(D:\softwares\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen141
))
(D:\softwares\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen142
)
(D:\softwares\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count194
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count195
\leftroot@=\count196
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count197
\DOTSCASE@=\count198
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen143
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count199
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count266
\dotsspace@=\muskip16
\c@parentequation=\count267
\dspbrk@lvl=\count268
\tag@help=\toks20
\row@=\count269
\column@=\count270
\maxfields@=\count271
\andhelp@=\toks21
\eqnshift@=\dimen144
\alignsep@=\dimen145
\tagshift@=\dimen146
\tagwidth@=\dimen147
\totwidth@=\dimen148
\lineht@=\dimen149
\@envbody=\toks22
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks23
