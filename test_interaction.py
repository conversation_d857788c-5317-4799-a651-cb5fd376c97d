#!/usr/bin/env python3
"""
测试新的交互功能
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

from render_image import compare_image_with_rendered_latex

def test_interaction():
    """测试交互功能"""
    print("=== 测试新的交互功能 ===")
    print()
    print("交互说明：")
    print("  - 按空格键：进入下一个LaTeX字符串的渲染和比较")
    print("  - 按ESC键：退出渲染程序")
    print()
    print("测试将显示几个LaTeX公式，请测试按键功能...")
    print("-" * 50)
    
    # 测试用的LaTeX公式
    test_formulas = [
        r"\frac{1.4y-1.2x}{1+1.2x}",
        r"\frac{a}{b}",
        r"\sum_{i=1}^{n} x_i",
        r"\int_0^1 f(x) dx",
        r"\binom{n}{k}",
    ]
    
    # 创建一个简单的测试图片（如果不存在的话）
    test_image_path = "test_image.png"
    if not Path(test_image_path).exists():
        # 创建一个简单的测试图片
        import matplotlib.pyplot as plt
        import numpy as np
        
        fig, ax = plt.subplots(figsize=(4, 2))
        ax.text(0.5, 0.5, '测试图片\nTest Image', ha='center', va='center', fontsize=16)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        plt.savefig(test_image_path, bbox_inches='tight', dpi=150)
        plt.close()
        print(f"创建了测试图片: {test_image_path}")
    
    # 测试交互
    for i, formula in enumerate(test_formulas):
        print(f"\n测试 {i+1}/{len(test_formulas)}: {formula}")
        
        # 调用对比函数
        action = compare_image_with_rendered_latex(test_image_path, formula)
        
        print(f"用户操作: {action}")
        
        if action == 'quit':
            print("用户选择退出，测试结束")
            break
        elif action == 'next':
            print("用户选择继续下一个")
            continue
    
    print("\n测试完成！")
    
    # 清理测试文件
    if Path(test_image_path).exists():
        os.remove(test_image_path)
        print(f"清理测试文件: {test_image_path}")

def test_keyboard_functionality():
    """测试键盘功能的说明"""
    print("\n=== 键盘功能测试说明 ===")
    print()
    print("新的交互模式已实现，功能如下：")
    print()
    print("🔹 空格键 (Space):")
    print("   - 功能：进入下一个LaTeX字符串的渲染和比较")
    print("   - 使用场景：当您查看完当前公式对比后，想要继续查看下一个")
    print()
    print("🔹 ESC键 (Escape):")
    print("   - 功能：退出整个渲染程序")
    print("   - 使用场景：当您想要完全退出程序时")
    print()
    print("🔹 界面提示:")
    print("   - 每个对比窗口底部都会显示操作提示")
    print("   - 控制台会显示当前处理进度和用户操作反馈")
    print()
    print("🔹 改进点:")
    print("   - 之前：只能按ESC进入下一个，无法退出程序")
    print("   - 现在：空格键进入下一个，ESC键退出程序")
    print("   - 增加了清晰的操作提示和进度显示")

def main():
    """主函数"""
    print("LaTeX渲染交互功能测试")
    print("=" * 50)
    
    # 显示功能说明
    test_keyboard_functionality()
    
    # 询问是否进行实际测试
    print("\n是否要进行实际的交互测试？")
    print("注意：这将打开matplotlib窗口，您可以测试按键功能")
    
    try:
        choice = input("\n输入 'y' 开始测试，其他键跳过: ").strip().lower()
        
        if choice == 'y':
            test_interaction()
        else:
            print("跳过实际测试")
    except KeyboardInterrupt:
        print("\n用户中断测试")
    
    print("\n测试程序结束")

if __name__ == "__main__":
    main()
