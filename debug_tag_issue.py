#!/usr/bin/env python3
"""
调试具体的\tag命令问题
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_specific_tag_issues():
    """测试可能导致\tag失败的具体情况"""
    print("=== 调试具体的\\tag命令问题 ===")
    
    # 可能有问题的\tag使用方式
    problematic_cases = [
        {
            "name": "gather*环境中的tag",
            "latex": r"\begin{gather*} E = mc^2 \tag{1} \end{gather*}",
            "description": "在无编号gather环境中使用tag"
        },
        {
            "name": "嵌套在当前处理逻辑中的tag",
            "latex": r"E = mc^2 \tag{Einstein}",
            "description": "裸公式+tag（会被包装在gather*中）"
        },
        {
            "name": "复杂的tag内容",
            "latex": r"\begin{equation} \int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2} \tag{$\star$} \end{equation}",
            "description": "tag中包含数学符号"
        },
        {
            "name": "多行公式的tag",
            "latex": r"\begin{align*} f(x) &= ax + b \tag{线性} \\ g(x) &= ax^2 + bx + c \tag{二次} \end{align*}",
            "description": "无编号align环境中的tag"
        },
        {
            "name": "中文环境下的tag",
            "latex": r"\begin{equation} 速度 = \frac{距离}{时间} \tag{物理公式} \end{equation}",
            "description": "包含中文的公式和tag"
        }
    ]
    
    try:
        from render_image import render_latex_to_image
        
        results = {}
        
        for i, test_case in enumerate(problematic_cases, 1):
            name = test_case["name"]
            latex = test_case["latex"]
            description = test_case["description"]
            
            print(f"\n调试 {i}: {name}")
            print(f"描述: {description}")
            print(f"LaTeX: {latex}")
            
            output_file = f"debug_tag_{i}_{name.replace(' ', '_')}.png"
            
            try:
                success = render_latex_to_image(latex, output_file)
                
                if success:
                    print(f"  ✅ 渲染成功: {output_file}")
                    results[name] = "成功"
                else:
                    print(f"  ❌ 渲染失败")
                    results[name] = "失败"
                    
            except Exception as e:
                print(f"  ❌ 渲染出错: {str(e)[:100]}...")
                results[name] = f"错误: {str(e)[:50]}..."
        
        return results
        
    except ImportError as e:
        print(f"❌ 无法导入render_image模块: {e}")
        return {}

def analyze_processing_logic():
    """分析当前的LaTeX处理逻辑对\tag的影响"""
    print("\n=== 分析LaTeX处理逻辑 ===")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))
        from render_image import _process_latex_string
        
        test_cases = [
            r"E = mc^2 \tag{1}",
            r"\begin{equation} E = mc^2 \tag{1} \end{equation}",
            r"\frac{a}{b} \tag{分数}",
        ]
        
        print("\n🔍 测试LaTeX字符串处理:")
        
        for i, latex in enumerate(test_cases, 1):
            print(f"\n测试 {i}:")
            print(f"  输入: {latex}")
            
            try:
                processed = _process_latex_string(latex)
                print(f"  处理后: {processed}")
                
                # 检查是否包含\tag
                if r'\tag' in processed:
                    print(f"  ✅ \\tag命令保留")
                else:
                    print(f"  ⚠️  \\tag命令可能被影响")
                    
            except Exception as e:
                print(f"  ❌ 处理出错: {e}")
        
    except ImportError:
        print("无法导入处理函数，跳过分析")

def check_template_compatibility():
    """检查模板对\tag的兼容性"""
    print("\n=== 检查模板兼容性 ===")
    
    print("\n📋 当前LaTeX模板分析:")
    print("文档类: standalone")
    print("宏包: ctex, amsmath, amsfonts, amssymb, mathtools")
    print("环境: 动态选择 (gather*, equation等)")
    
    print("\n✅ \\tag命令兼容性:")
    print("• amsmath宏包: 提供\\tag命令支持")
    print("• mathtools宏包: 扩展amsmath功能")
    print("• standalone文档类: 支持数学环境")
    print("• ctex宏包: 支持中文tag")
    
    print("\n🔍 可能的问题点:")
    print("1. gather*环境: 无编号环境中的\\tag可能有特殊行为")
    print("2. 字符串处理: 可能影响\\tag命令的完整性")
    print("3. 字体设置: 可能影响tag的显示")

def provide_solutions():
    """提供解决方案"""
    print("\n=== 解决方案建议 ===")
    
    print("\n💡 如果\\tag仍然不工作，可能的原因和解决方案:")
    
    print("\n1. **特定LaTeX代码问题**:")
    print("   • 请提供具体的不工作的LaTeX代码")
    print("   • 检查\\tag的语法是否正确")
    print("   • 确保\\tag在正确的数学环境中")
    
    print("\n2. **环境冲突**:")
    print("   • 避免在gather*环境中使用\\tag")
    print("   • 使用equation或gather环境代替")
    print("   • 检查是否有其他命令与\\tag冲突")
    
    print("\n3. **字符串处理问题**:")
    print("   • 检查LaTeX字符串是否被正确传递")
    print("   • 确保\\tag命令没有被意外修改")
    
    print("\n4. **替代方案**:")
    print("   如果\\tag确实不工作，可以使用:")
    print("   • \\text{(标签)} - 在公式末尾添加文本标签")
    print("   • \\qquad\\text{(标签)} - 带间距的标签")
    print("   • 手动编号: \\text{(1)}, \\text{(2)} 等")
    
    print("\n📝 推荐的\\tag使用方式:")
    print("```latex")
    print("\\begin{equation}")
    print("E = mc^2 \\tag{爱因斯坦质能方程}")
    print("\\end{equation}")
    print("```")

def main():
    """主函数"""
    print("LaTeX \\tag命令问题调试")
    print("=" * 50)
    
    print("\n🎯 调试目标:")
    print("  深入分析\\tag命令可能的问题")
    print("  测试边缘情况和特殊用法")
    print("  提供具体的解决方案")
    
    # 分析处理逻辑
    analyze_processing_logic()
    
    # 检查模板兼容性
    check_template_compatibility()
    
    # 测试问题情况
    print("\n🧪 测试可能有问题的情况...")
    results = test_specific_tag_issues()
    
    # 提供解决方案
    provide_solutions()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 调试结果总结")
    print("=" * 50)
    
    if results:
        success_count = sum(1 for result in results.values() if result == "成功")
        total_count = len(results)
        
        print(f"\n📊 边缘情况测试:")
        print(f"  测试数: {total_count}")
        print(f"  成功数: {success_count}")
        print(f"  成功率: {success_count/total_count*100:.1f}%")
        
        for name, result in results.items():
            status = "✅" if result == "成功" else "❌"
            print(f"  {status} {name}: {result}")
    
    print(f"\n💡 结论:")
    print(f"  基于测试结果，当前渲染脚本对\\tag命令有很好的支持")
    print(f"  如果您遇到具体问题，请提供:")
    print(f"  1. 具体的LaTeX代码")
    print(f"  2. 错误信息或现象描述")
    print(f"  3. 期望的渲染效果")

if __name__ == "__main__":
    main()
