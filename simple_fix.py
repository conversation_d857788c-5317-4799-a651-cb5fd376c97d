#!/usr/bin/env python3
import re

with open('msr/pred_texteller_3.txt', 'r', encoding='utf-8') as f:
    content = f.read()

# First, let's understand the actual format by examining the content
lines = content.splitlines()
print(f"Total lines: {len(lines)}")

fixed_lines = []
i = 0

while i < len(lines):
    line = lines[i]
    
    # Skip empty lines
    if not line.strip():
        i += 1
        continue
    
    # Look for pattern: spaces + number + arrow + content
    match = re.match(r'^(\s*\d+)(.)(.*)', line)
    if match:
        line_prefix = match.group(1)
        arrow_char = match.group(2)
        content = match.group(3)
        
        # Check if this is a broken formula (starts with \[ but doesn't end with \])
        if content.startswith('\\[') and not content.endswith('\\]'):
            # This is a broken formula, collect all parts
            formula_parts = [content]
            j = i + 1
            
            # Look for the ending
            while j < len(lines):
                next_line = lines[j]
                if not next_line.strip():
                    j += 1
                    continue
                
                # Check if this line has a line number
                next_match = re.match(r'^(\s*\d+)(.)(.*)', next_line)
                if next_match:
                    next_content = next_match.group(3)
                    formula_parts.append(next_content)
                    if next_content.endswith('\\]'):
                        # Found the end
                        break
                else:
                    # Line without number, add as continuation
                    formula_parts.append(next_line.strip())
                    if next_line.strip().endswith('\\]'):
                        break
                
                j += 1
            
            # Join all parts
            complete_formula = ' '.join(formula_parts)
            fixed_lines.append(f"{line_prefix}{arrow_char}{complete_formula}")
            i = j + 1
        else:
            # Line is already complete
            fixed_lines.append(line)
            i += 1
    else:
        # Line doesn't match expected format, skip
        print(f"Skipping line: {line[:50]}...")
        i += 1

print(f"Generated {len(fixed_lines)} fixed lines")

# Write the result
with open('msr/pred_texteller_3_fixed.txt', 'w', encoding='utf-8') as f:
    for line in fixed_lines:
        f.write(line + '\n')

print("Fixed file created successfully!")