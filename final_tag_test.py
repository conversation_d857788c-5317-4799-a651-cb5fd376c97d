#!/usr/bin/env python3
"""
最终\tag命令测试
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def final_tag_test():
    """最终\tag命令测试"""
    print("=== 最终\\tag命令测试 ===")
    
    try:
        from render_image import render_latex_to_image
        
        # 测试用户的具体案例
        test_latex = "a+b=c \\tag{2}"
        output_file = "final_tag_test.png"
        
        print(f"测试LaTeX: {test_latex}")
        print("期望结果: 显示 a+b=c 公式，右侧有编号 (2)")
        
        success = render_latex_to_image(test_latex, output_file)
        
        if success:
            print(f"✅ 渲染成功: {output_file}")
            
            # 检查文件
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"📊 文件大小: {file_size} 字节")
                
                if file_size > 1000:
                    print("✅ 文件大小正常，应该包含完整的渲染内容")
                    print("📁 请查看图片确认是否显示了 (2) 编号")
                    return True
                else:
                    print("⚠️  文件大小较小，可能渲染不完整")
                    return False
            else:
                print("❌ 输出文件不存在")
                return False
        else:
            print("❌ 渲染失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_multiple_tags():
    """测试多个\tag案例"""
    print("\n=== 测试多个\\tag案例 ===")
    
    test_cases = [
        ("a+b=c \\tag{2}", "用户原始案例"),
        ("E = mc^2 \\tag{Einstein}", "文字标签"),
        ("\\frac{a}{b} \\tag{1}", "分数+标签"),
        ("x^2 + y^2 = z^2 \\tag{勾股定理}", "中文标签"),
    ]
    
    try:
        from render_image import render_latex_to_image
        
        results = {}
        
        for i, (latex, description) in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {description}")
            print(f"LaTeX: {latex}")
            
            output_file = f"multi_tag_test_{i}.png"
            
            try:
                success = render_latex_to_image(latex, output_file)
                
                if success:
                    print(f"  ✅ 渲染成功: {output_file}")
                    results[description] = "成功"
                else:
                    print(f"  ❌ 渲染失败")
                    results[description] = "失败"
                    
            except Exception as e:
                print(f"  ❌ 渲染出错: {str(e)[:50]}...")
                results[description] = "错误"
        
        return results
        
    except Exception as e:
        print(f"❌ 多案例测试失败: {e}")
        return {}

def main():
    """主函数"""
    print("LaTeX \\tag命令最终测试")
    print("=" * 40)
    
    print("\n🎯 测试目标:")
    print("  验证修复后的\\tag命令是否能正确渲染编号")
    print("  特别测试 'a+b=c \\tag{2}' 是否显示 (2)")
    
    print("\n🔧 修复内容:")
    print("  1. 智能环境选择：包含\\tag时使用gather环境")
    print("  2. 模板修复：解决\\fontsize和gather环境的冲突")
    print("  3. 特殊处理：即使简单公式也支持\\tag")
    
    # 运行最终测试
    main_success = final_tag_test()
    
    # 测试多个案例
    multi_results = test_multiple_tags()
    
    # 总结结果
    print("\n" + "=" * 40)
    print("🎯 最终测试结果")
    print("=" * 40)
    
    if main_success:
        print("✅ 主要测试通过")
    else:
        print("❌ 主要测试失败")
    
    if multi_results:
        success_count = sum(1 for result in multi_results.values() if result == "成功")
        total_count = len(multi_results)
        
        print(f"\n📊 多案例测试:")
        print(f"  总数: {total_count}")
        print(f"  成功: {success_count}")
        print(f"  成功率: {success_count/total_count*100:.1f}%")
        
        for description, result in multi_results.items():
            status = "✅" if result == "成功" else "❌"
            print(f"  {status} {description}: {result}")
    
    overall_success = main_success and (success_count == total_count if multi_results else False)
    
    if overall_success:
        print(f"\n🎉 \\tag命令修复完全成功！")
        print(f"\n📋 现在支持的功能:")
        print(f"  • 简单公式+标签: a+b=c \\tag{{2}}")
        print(f"  • 复杂公式+标签: \\frac{{a}}{{b}} \\tag{{1}}")
        print(f"  • 中文标签: 公式 \\tag{{中文}}")
        print(f"  • 数字标签: 公式 \\tag{{123}}")
        
        print(f"\n💡 使用说明:")
        print(f"  现在您可以在任何LaTeX字符串中使用\\tag命令")
        print(f"  系统会自动选择合适的环境来支持标签显示")
        print(f"  标签会显示在公式的右侧，格式为 (标签内容)")
    else:
        print(f"\n⚠️  \\tag命令修复可能不完整")
        print(f"请检查失败的测试案例")
    
    print(f"\n📁 生成的测试文件:")
    print(f"  final_tag_test.png - 主要测试结果")
    print(f"  multi_tag_test_*.png - 多案例测试结果")
    print(f"  请查看这些图片确认\\tag编号是否正确显示")

if __name__ == "__main__":
    main()
