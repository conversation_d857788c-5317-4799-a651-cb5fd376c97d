#!/usr/bin/env python3
"""
验证修复效果的测试脚本
"""

import sys
import os
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import numpy as np

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

from render_image import render_latex_to_image, _process_latex_string

def analyze_image_layout(image_path):
    """分析图片布局，返回宽高比"""
    try:
        img = mpimg.imread(image_path)
        
        # 转换为灰度图
        if len(img.shape) == 3:
            gray = np.mean(img, axis=2)
        else:
            gray = img
        
        # 找到非白色像素
        non_white = gray < 0.9
        
        # 找到内容的边界框
        rows = np.any(non_white, axis=1)
        cols = np.any(non_white, axis=0)
        
        if not np.any(rows) or not np.any(cols):
            return None
        
        rmin, rmax = np.where(rows)[0][[0, -1]]
        cmin, cmax = np.where(cols)[0][[0, -1]]
        
        height = rmax - rmin + 1
        width = cmax - cmin + 1
        aspect_ratio = width / height
        
        return {
            "height": height,
            "width": width,
            "aspect_ratio": aspect_ratio
        }
        
    except Exception as e:
        print(f"分析图片失败: {e}")
        return None

def test_fraction_fix():
    """测试分数修复效果"""
    print("=== 测试分数渲染修复效果 ===\n")
    
    # 用户报告的问题案例
    test_case = r"\frac{1.4y-1.2x}{1+1.2x}"
    
    print(f"测试案例: {test_case}")
    
    # 查看处理后的LaTeX
    processed = _process_latex_string(test_case)
    print(f"处理后的LaTeX:\n{processed}")
    
    # 渲染测试
    output_file = "fixed_fraction_test.png"
    print(f"\n正在渲染到 {output_file}...")
    
    success = render_latex_to_image(test_case, output_file, dpi=300)
    
    if success:
        print("✅ 渲染成功！")
        
        # 分析渲染结果
        layout_info = analyze_image_layout(output_file)
        if layout_info:
            print(f"图片尺寸: {layout_info['height']}x{layout_info['width']} (高x宽)")
            print(f"宽高比: {layout_info['aspect_ratio']:.2f}")
            
            if layout_info['aspect_ratio'] < 1.5:
                print("🎉 成功！分数显示为垂直结构")
                return True
            else:
                print("⚠️  仍然是水平分数，需要进一步调试")
                return False
        else:
            print("❌ 无法分析图片布局")
            return False
    else:
        print("❌ 渲染失败")
        return False

def test_multiple_cases():
    """测试多个分数案例"""
    print("\n=== 测试多个分数案例 ===\n")
    
    test_cases = [
        r"\frac{1.4y-1.2x}{1+1.2x}",  # 用户原始案例
        r"\frac{a}{b}",  # 简单分数
        r"\frac{x^2 + 2x + 1}{x - 1}",  # 复杂分数
        r"\frac{\sin(x)}{\cos(x)}",  # 三角函数分数
        r"\frac{1}{\sqrt{2\pi}}",  # 包含根号的分数
    ]
    
    results = {}
    
    for i, case in enumerate(test_cases):
        print(f"测试案例 {i+1}: {case}")
        
        # 查看处理结果
        processed = _process_latex_string(case)
        print(f"处理后: {processed}")
        
        # 渲染
        output_file = f"test_case_{i+1}_fixed.png"
        success = render_latex_to_image(case, output_file, dpi=300)
        
        if success:
            layout_info = analyze_image_layout(output_file)
            if layout_info:
                aspect_ratio = layout_info['aspect_ratio']
                is_vertical = aspect_ratio < 1.5
                print(f"宽高比: {aspect_ratio:.2f} - {'✅ 垂直' if is_vertical else '❌ 水平'}")
                results[case] = is_vertical
            else:
                print("❌ 分析失败")
                results[case] = False
        else:
            print("❌ 渲染失败")
            results[case] = False
        
        print()
    
    return results

def test_non_fraction_compatibility():
    """测试非分数表达式的兼容性"""
    print("=== 测试非分数表达式兼容性 ===\n")
    
    non_fraction_cases = [
        r"x^2 + y^2 = z^2",  # 简单方程
        r"\sum_{i=1}^{n} x_i",  # 求和
        r"\int_0^1 f(x) dx",  # 积分
        r"\alpha + \beta = \gamma",  # 希腊字母
        r"\sqrt{x^2 + y^2}",  # 根号
    ]
    
    for i, case in enumerate(non_fraction_cases):
        print(f"测试非分数案例 {i+1}: {case}")
        
        processed = _process_latex_string(case)
        print(f"处理后: {processed}")
        
        output_file = f"non_frac_test_{i+1}.png"
        success = render_latex_to_image(case, output_file, dpi=300)
        print(f"渲染结果: {'✅ 成功' if success else '❌ 失败'}")
        print()

def main():
    """主函数"""
    print("开始验证LaTeX分数渲染修复效果...\n")
    
    # 1. 测试主要问题案例
    main_success = test_fraction_fix()
    
    # 2. 测试多个分数案例
    multiple_results = test_multiple_cases()
    
    # 3. 测试兼容性
    test_non_fraction_compatibility()
    
    # 4. 总结结果
    print("=== 修复效果总结 ===")
    if main_success:
        print("🎉 主要问题已修复：用户报告的分数案例现在正确显示为垂直结构")
    else:
        print("❌ 主要问题未完全修复")
    
    if multiple_results:
        vertical_count = sum(multiple_results.values())
        total_count = len(multiple_results)
        print(f"📊 多案例测试：{vertical_count}/{total_count} 个分数正确显示为垂直结构")
        
        if vertical_count == total_count:
            print("✅ 所有分数案例都已修复")
        elif vertical_count > 0:
            print("⚠️  部分分数案例已修复，可能需要进一步优化")
        else:
            print("❌ 分数案例修复失败")

if __name__ == "__main__":
    main()
