This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.7.15)  25 JUL 2025 11:56
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./test_standard_equation.tex
(test_standard_equation.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(D:\softwares\MiKTeX\tex/latex/standalone\standalone.cls
Document Class: standalone 2022/10/10 v1.3b Class to compile TeX sub-files stan
dalone
(D:\softwares\MiKTeX\tex/latex/tools\shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
)
(D:\softwares\MiKTeX\tex/generic/iftex\ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.

(D:\softwares\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
(D:\softwares\MiKTeX\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(D:\softwares\MiKTeX\tex/generic/xkeyval\xkeyval.tex
(D:\softwares\MiKTeX\tex/generic/xkeyval\xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18

(D:\softwares\MiKTeX\tex/generic/xkeyval\keyval.tex))
\XKV@depth=\count183
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\sa@internal=\count184
\c@sapage=\count185

(D:\softwares\MiKTeX\tex/latex/standalone\standalone.cfg
File: standalone.cfg 2022/10/10 v1.3b Default configuration file for 'standalon
e' class
)
(D:\softwares\MiKTeX\tex/latex/base\article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(D:\softwares\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count186
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@subparagraph=\count191
\c@figure=\count192
\c@table=\count193
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
\sa@box=\box51
)
(D:\softwares\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(D:\softwares\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(D:\softwares\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen141
))
(D:\softwares\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen142
)
(D:\softwares\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count194
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count195
\leftroot@=\count196
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count197
\DOTSCASE@=\count198
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen143
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count199
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count266
\dotsspace@=\muskip16
\c@parentequation=\count267
\dspbrk@lvl=\count268
\tag@help=\toks20
\row@=\count269
\column@=\count270
\maxfields@=\count271
\andhelp@=\toks21
\eqnshift@=\dimen144
\alignsep@=\dimen145
\tagshift@=\dimen146
\tagwidth@=\dimen147
\totwidth@=\dimen148
\lineht@=\dimen149
\@envbody=\toks22
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(D:\softwares\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count272
\l__pdf_internal_box=\box54
\g__pdf_backend_object_int=\count273
\g__pdf_backend_annotation_int=\count274
\g__pdf_backend_link_int=\count275
)
No file test_standard_equation.aux.
\openout1 = `test_standard_equation.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 4.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 4
.
(D:\softwares\MiKTeX\tex/latex/base\ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.

! Missing $ inserted.
<inserted text> 
                $
l.5 \begin{equation}
                    
? ＀＀
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
1 or ... or 9 to ignore the next 1 to 9 tokens of input,
H for help, X to quit.
? 
! Interruption.
<to be read again> 
                   \mathopen 
l.5 \begin{equation}
                    
? python test_tag_position_fix.py
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
H for help, X to quit.
? 
! You can't use `\eqno' in math mode.
\veqno ->\@kernel@eqno 
                       \aftergroup \ignorespaces 
l.7 \end{equation}
                  
? ＀
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
1 or ... or 9 to ignore the next 1 to 9 tokens of input,
H for help, X to quit.
? 
! Interruption.
<to be read again> 
                   \aftergroup 
l.7 \end{equation}
                  
? python quick_tag_test.py
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
H for help, X to quit.
? 
! Interruption.
<to be read again> 
                   \global 
l.7 \end{equation}
                  
? ＀
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
H for help, X to quit.
? 
! Missing $ inserted.
<inserted text> 
                $
l.7 \end{equation}
                  
? python test_crop_issue.py
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
1 or ... or 9 to ignore the next 1 to 9 tokens of input,
H for help, X to quit.
? 
! Interruption.
<to be read again> 
                   \endgroup 
l.7 \end{equation}
                  
? ＀
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
H for help, X to quit.
? 
! Interruption.
\__seq_item:n ..._#1_tl}{g__mark_page_first_#1_tl}
                                                  \tl_gset_eq:cc {g__mark_pr...
l.8 \end{document}
                  
? python test_tag_spacin