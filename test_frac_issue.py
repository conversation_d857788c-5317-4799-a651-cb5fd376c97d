#!/usr/bin/env python3
"""
详细测试脚本：分析LaTeX分数渲染问题的根本原因
"""

import sys
import os
import tempfile
import subprocess
from pathlib import Path
import pdf2image

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

from render_image import render_latex_to_image, _process_latex_string

def create_custom_latex_template(include_xfrac=True, use_standalone=True):
    """创建自定义LaTeX模板用于测试"""
    if use_standalone:
        doc_class = r"\documentclass[border=2pt]{standalone}"
    else:
        doc_class = r"\documentclass{article}"

    packages = [
        r"\usepackage{ctex}",
        r"\usepackage{amsmath}",
        r"\usepackage{amsfonts}",
        r"\usepackage{amssymb}",
        r"\usepackage{mathtools}",
    ]

    if include_xfrac:
        packages.append(r"\usepackage{xfrac}")

    template = f"""
    {doc_class}
    {chr(10).join(packages)}
    \\begin{{document}}
    %s
    \\end{{document}}
    """
    return template

def test_latex_template_variations(latex_string):
    """测试不同LaTeX模板配置的效果"""
    print(f"\n=== 测试LaTeX模板变化对 '{latex_string}' 的影响 ===")

    test_configs = [
        ("标准配置(含xfrac+standalone)", True, True),
        ("移除xfrac宏包", False, True),
        ("使用article文档类", True, False),
        ("移除xfrac+使用article", False, False),
    ]

    results = {}

    for config_name, include_xfrac, use_standalone in test_configs:
        print(f"\n--- {config_name} ---")
        template = create_custom_latex_template(include_xfrac, use_standalone)

        # 处理数学模式
        processed_latex = _process_latex_string(latex_string)
        print(f"处理后的LaTeX: {processed_latex}")

        # 创建完整的LaTeX代码
        full_latex = template % processed_latex

        # 尝试渲染
        success = render_with_custom_template(full_latex, f"template_test_{config_name.replace(' ', '_').replace('(', '').replace(')', '')}.png")
        results[config_name] = success
        print(f"渲染结果: {'✅ 成功' if success else '❌ 失败'}")

    return results

def render_with_custom_template(latex_code, output_path, dpi=300):
    """使用自定义LaTeX代码进行渲染"""
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        tex_path = temp_path / "custom_formula.tex"
        pdf_path = temp_path / "custom_formula.pdf"

        with open(tex_path, "w", encoding='utf-8') as f:
            f.write(latex_code)

        try:
            process = subprocess.run(
                ["xelatex", "-interaction=nonstopmode", "custom_formula.tex"],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=temp_dir,
                encoding='utf-8',
                errors='ignore'
            )

            if not pdf_path.exists():
                print(f"PDF生成失败: {process.stderr}")
                return False

            images = pdf2image.convert_from_path(
                pdf_path,
                dpi=dpi,
                fmt='png',
                transparent=True,
                timeout=60
            )

            if images:
                images[0].save(output_path, 'PNG')
                return True
            else:
                print("PDF转图片失败")
                return False

        except Exception as e:
            print(f"渲染过程出错: {e}")
            return False

def test_math_mode_variations():
    """测试不同数学模式的效果"""
    print(f"\n=== 测试数学模式变化 ===")

    base_frac = r"\frac{1.4y-1.2x}{1+1.2x}"

    math_modes = [
        ("裸分数", base_frac),
        ("内联数学模式", f"${base_frac}$"),
        ("显示数学模式", f"\\[{base_frac}\\]"),
        ("双美元符号", f"$${base_frac}$$"),
        ("带displaystyle的内联", f"$\\displaystyle {base_frac}$"),
        ("带displaystyle的显示", f"\\[\\displaystyle {base_frac}\\]"),
        ("使用dfrac", r"\dfrac{1.4y-1.2x}{1+1.2x}"),
        ("使用tfrac", r"\tfrac{1.4y-1.2x}{1+1.2x}"),
    ]

    for mode_name, latex_expr in math_modes:
        print(f"\n--- {mode_name}: {latex_expr} ---")
        processed = _process_latex_string(latex_expr)
        print(f"处理后: {processed}")

        output_file = f"math_mode_{mode_name.replace(' ', '_')}.png"
        success = render_latex_to_image(latex_expr, output_file, dpi=300)
        print(f"渲染结果: {'✅ 成功' if success else '❌ 失败'}")

def analyze_problem_root_cause():
    """分析问题根本原因的主函数"""
    print("开始分析LaTeX分数渲染问题的根本原因...")

    # 用户报告的具体案例
    problem_case = r"\frac{1.4y-1.2x}{1+1.2x}"

    print(f"问题案例: {problem_case}")

    # 1. 测试当前实现
    print("\n1. 测试当前实现...")
    processed = _process_latex_string(problem_case)
    print(f"当前处理结果: {processed}")
    success = render_latex_to_image(problem_case, "current_implementation.png", dpi=300)
    print(f"当前实现渲染: {'✅ 成功' if success else '❌ 失败'}")

    # 2. 测试不同LaTeX模板配置
    print("\n2. 测试不同LaTeX模板配置...")
    template_results = test_latex_template_variations(problem_case)

    # 3. 测试不同数学模式
    print("\n3. 测试不同数学模式...")
    test_math_mode_variations()

    # 4. 生成分析报告
    print("\n=== 问题分析报告 ===")
    print("基于测试结果，问题可能的根本原因：")
    print("1. xfrac宏包可能与标准\\frac命令产生冲突")
    print("2. standalone文档类可能影响数学公式的显示样式")
    print("3. 数学模式处理可能不够完善")

    print("\n模板测试结果:")
    for config, result in template_results.items():
        print(f"  {config}: {'✅ 成功' if result else '❌ 失败'}")

    return template_results

if __name__ == "__main__":
    analyze_problem_root_cause()
