#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TextIn 公式识别 → LaTeX 字符串提取示例
作者：Your Name
日期：2024-07-24
"""

import os
import cv2
import json
import base64
import urllib.request
import urllib.error
import mimetypes
from typing import List, Tuple

# ========== 1. 请替换成自己的 ==========
APP_ID     = "dd808fc88a97148599ade70797e98952"
SECRET_KEY = "2f76a87e975e17aa2ef835f449b36eaa"
API_URL    = "https://api.textin.com/ai/service/v1/pdf_to_markdown"
# =======================================

def build_request_headers() -> dict:
    return {
        "x-ti-app-id": APP_ID,
        "x-ti-secret-code": SECRET_KEY,
        "Content-Type": "application/octet-stream"
    }

def build_url_params() -> str:
    """
    打开公式识别（0=全部识别）、关闭正文、关闭表格、返回 Markdown。
    其余默认即可满足公式提取需求。
    """
    params = {
        "formula_level": 1,      # 0 = 行内+行间公式都识别, 1 仅识别行间公式，行内公式不识别
        "apply_document_tree": 0,# 不生成标题层级
        "markdown_details": 1,   # 返回 markdown 便于我们提取公式
        "page_details": 0,       # 不返回逐页详情，减少流量
    }
    return "?" + "&".join(f"{k}={v}" for k, v in params.items())

def upload_file(file_path: str) -> dict:
    """上传单文件 -> 解析后的 JSON"""
    url = API_URL + build_url_params()
    with open(file_path, "rb") as f:
        data = f.read()

    req = urllib.request.Request(url, data=data, headers=build_request_headers())
    try:
        resp = urllib.request.urlopen(req, timeout=120)
        return json.loads(resp.read().decode())
    except urllib.error.HTTPError as e:
        err = e.read().decode()
        raise RuntimeError(f"HTTP {e.code}: {err}")

def extract_latex_and_bbox(js: dict) -> List[Tuple[str, List[int]]]:
    formulas = []
    for para in js["result"]["detail"]:
        if "formula" in para.get("tags", []):
            formulas.append((para["text"], para["position"]))
    return formulas

def process_one(file_path: str) -> Tuple[str, List[Tuple[str, List[int]]]]:
    if not file_path.lower().endswith(".png"):
        raise ValueError("只支持 PNG 图像")
    js = upload_file(file_path)
    if js.get("code") != 200:
        raise RuntimeError(js.get("message"))
    return os.path.basename(file_path), extract_latex_and_bbox(js)

def textin_demo_single():
    path = r"D:\datasets\user_data_dlf\image1\DLF_000002.png"
    out_path = r"D:\datasets\MSR\formula_set_crop_textin"
    out_txt = r"D:\datasets\MSR\textin_pred.txt"

    name, formulas = process_one(path)

    print("\n==== LaTeX + bbox ====")
    cnt = 1
    img = cv2.imread(path)
    base = os.path.splitext(os.path.basename(path))[0]
    for latex, bbox in formulas:
        print("LaTeX:", latex)
        print("bbox :", bbox)
        print()

        x1, y1, x2, y2, x3, y3, x4, y4 = bbox
        img_crop = img[y1:y3, x1:x3, :]
        cv2.imwrite(os.path.join(out_path, f"{base}_{cnt:02d}.png"), img_crop)
        cnt += 1

        with open(out_txt, "a+") as fr:
            fr.write(latex + "\n")

# =======================
if __name__ == "__main__":
    # textin_demo_single()

    root_path = r"D:\datasets\MSR\formula_set_select2"
    out_path = r"D:\datasets\MSR\formula_set_crop_textin"
    out_txt = r"D:\datasets\MSR\textin_pred.txt"
    list_ = os.listdir(root_path)
    out_list = []
    for imname in list_:
        base, _ = os.path.splitext(imname)
        if base[:-3] not in out_list:
            out_list.append(base[:-3])
    # # with open("formula_set_select2.txt", "w") as f:
    # #     f.write("\n".join(out_list))
    # print(out_list)
    # print(len(out_list))

    out_list.sort()

    for base in out_list[85:]:
        path = os.path.join(r"D:\datasets\DLA\user_data_20250716\images_com", base + ".png")

        name, formulas = process_one(path)

        print("\n==== LaTeX + bbox ====")
        cnt = 1
        img = cv2.imread(path)
        base = os.path.splitext(os.path.basename(path))[0]
        for latex, bbox in formulas:
            print("LaTeX:", latex)
            print("bbox :", bbox)
            print()

            x1, y1, x2, y2, x3, y3, x4, y4 = bbox
            img_crop = img[y1:y3, x1:x3, :]
            cv2.imwrite(os.path.join(out_path, f"{base}_{cnt:02d}.png"), img_crop)
            cnt += 1

            with open(out_txt, "a+", encoding="utf-8") as fa:
                fa.write(latex + "\n")


    