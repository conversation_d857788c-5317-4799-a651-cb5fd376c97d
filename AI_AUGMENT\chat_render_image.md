# 发现分数\frac 渲染出来不对
（1）
@e:\codes\OCR\MSR/msr\tools\render_image.py   是我用来渲染latex字符串，及与对应图像进行对比的一个工具。这个latex字符串是我公式识别算法的输出。   
现在，我发现对于\frac分数的一些字符串，该工具不能很好进行渲染。   
我能明确这个latex字符串是正确的，是能够正常渲染的。你现在所需要做的，   
1. 帮我分析是什么原因导致的。   
2. 帮我修复该问题。 

（2）
你可以继续完成你的任务。我的目标， 是在我的渲染脚本 @e:\codes\OCR\MSR/msr\tools\render_image.py 里面可以正常渲染分数公式。

（3）
请你继续完成你的任务，中间不需要再问我，直到你本次任务的完成。

# 发现渲染公式太小了
很高兴你解决了 @e:\codes\OCR\MSR/msr\tools\render_image.py 无法正常渲染分数公式的问题。但现在出现了新的问题：渲染出来的公式太小了，无法在展示的窗口中较好地看见。
我需要你分析为什么会如此，并为我解决这个问题。

# 优化交互模式
在渲染方面已经不存在问题了。但是我们在使用体验上还可以有优化空间。 @e:\codes\OCR\MSR/msr\tools\render_image.py 在渲染latex字符串并进行比较的时候，当前的交互行为为：当我按下"esc"或者按下关闭窗口会进入下一张latex字符串的渲染和比较。但是没有退出渲染程序的交互行为。

我想将交互模式变为如下形式：
1. 当我按下“空格键”的时候，进入下一个latex字符串的渲染和比较；
2. 当我按下“Esc”按键的时候，退出渲染程序。
