import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from pathlib import Path
import subprocess
import tempfile
import shutil
import os
import pdf2image
import matplotlib.gridspec as gridspec

# ----------------------------------------------------------------------------
# 核心渲染函数 (从你的代码中提炼和优化)
# ----------------------------------------------------------------------------

def _crop_to_content(image):
    """
    裁剪图片到内容区域，去除多余的空白

    Args:
        image: PIL Image对象

    Returns:
        PIL Image: 裁剪后的图片
    """
    import numpy as np

    # 转换为numpy数组
    img_array = np.array(image)

    # 如果是RGBA图片，使用alpha通道检测内容
    if len(img_array.shape) == 3 and img_array.shape[2] == 4:
        # 使用alpha通道检测非透明区域
        content_mask = img_array[:, :, 3] > 10  # alpha > 10
    else:
        # 如果是RGB图片，检测非白色区域
        if len(img_array.shape) == 3:
            # 转换为灰度
            gray = np.mean(img_array, axis=2)
        else:
            gray = img_array
        content_mask = gray < 250  # 非白色区域

    # 找到内容的边界
    rows = np.any(content_mask, axis=1)
    cols = np.any(content_mask, axis=0)

    if not np.any(rows) or not np.any(cols):
        # 如果没有找到内容，返回原图
        return image

    # 获取内容区域的边界
    rmin, rmax = np.where(rows)[0][[0, -1]]
    cmin, cmax = np.where(cols)[0][[0, -1]]

    # 添加一些边距（5像素）
    margin = 5
    rmin = max(0, rmin - margin)
    rmax = min(img_array.shape[0] - 1, rmax + margin)
    cmin = max(0, cmin - margin)
    cmax = min(img_array.shape[1] - 1, cmax + margin)

    # 裁剪图片
    cropped_image = image.crop((cmin, rmin, cmax + 1, rmax + 1))

    return cropped_image


def _needs_display_style(latex_string: str) -> bool:
    """
    Check if the LaTeX string contains commands that benefit from display style.

    Args:
        latex_string (str): LaTeX string to check

    Returns:
        bool: True if display style is recommended
    """
    display_style_commands = [
        '\\frac', '\\dfrac', '\\tfrac', '\\cfrac',  # Fractions
        '\\binom', '\\dbinom', '\\tbinom',  # Binomial coefficients
        '\\sum', '\\prod', '\\int', '\\oint',  # Large operators
        '\\lim', '\\limsup', '\\liminf',  # Limits
    ]
    return any(cmd in latex_string for cmd in display_style_commands)


def _process_latex_string(latex_string: str) -> str:
    """
    Process LaTeX string to ensure proper math mode and handle special cases.

    Enhanced processing for fractions and display-style commands:
    - Uses gather* environment with \\displaystyle for fractions to ensure vertical layout
    - Handles various fraction commands: \\frac, \\dfrac, \\tfrac, \\cfrac, \\binom, etc.
    - Fixes standalone document class compatibility issues

    Args:
        latex_string (str): Raw LaTeX string

    Returns:
        str: Processed LaTeX string with proper math mode
    """
    # Remove leading/trailing whitespace
    latex_string = latex_string.strip()
    
    # Check if already in math mode
    has_math_delimiters = (
        latex_string.startswith('$') and latex_string.endswith('$') or
        latex_string.startswith('\\[') and latex_string.endswith('\\]') or
        latex_string.startswith('\\(') and latex_string.endswith('\\)') or
        '\\begin{equation' in latex_string or
        '\\begin{align' in latex_string or
        '\\begin{gather' in latex_string
    )
    
    # Check if contains math commands that require math mode
    math_commands = [
        # Fractions and binomials
        '\\frac', '\\dfrac', '\\tfrac', '\\cfrac', '\\binom', '\\dbinom', '\\tbinom',
        # Roots and operators
        '\\sqrt', '\\sum', '\\int', '\\prod', '\\lim', '\\limsup', '\\liminf', '\\oint',
        # Greek letters
        '\\alpha', '\\beta', '\\gamma', '\\delta', '\\epsilon',
        '\\theta', '\\lambda', '\\mu', '\\pi', '\\sigma', '\\phi',
        '\\psi', '\\omega', '\\Gamma', '\\Delta', '\\Theta', '\\Lambda',
        '\\Pi', '\\Sigma', '\\Phi', '\\Psi', '\\Omega',
        # Operators and symbols
        '^', '_', '\\cdot', '\\times', '\\div', '\\pm', '\\mp',
        '\\leq', '\\geq', '\\neq', '\\approx', '\\equiv', '\\sim',
        '\\infty', '\\partial', '\\nabla', '\\exists', '\\forall'
    ]

    needs_math_mode = any(cmd in latex_string for cmd in math_commands)
    
    # If it needs math mode but doesn't have delimiters, add them
    if needs_math_mode and not has_math_delimiters:
        # Use gather* environment for expressions that benefit from display style
        # This fixes the issue with standalone document class rendering fractions horizontally
        if _needs_display_style(latex_string):
            # Add \displaystyle for extra assurance of proper rendering
            return f'\\begin{{gather*}}\n\\displaystyle {latex_string}\n\\end{{gather*}}'
        else:
            return f'${latex_string}$'

    # Even if already in math mode, check if we need to convert to display math for better rendering
    if _needs_display_style(latex_string) and has_math_delimiters:
        # If it's in inline math mode ($...$), convert to gather* environment for proper fraction rendering
        if latex_string.startswith('$') and latex_string.endswith('$') and not latex_string.startswith('$$'):
            # Remove the $ delimiters and use gather* environment with \displaystyle
            inner_content = latex_string[1:-1]
            return f'\\begin{{gather*}}\n\\displaystyle {inner_content}\n\\end{{gather*}}'
        # If it's already in \[...\] mode, convert to gather* for standalone compatibility
        elif latex_string.startswith('\\[') and latex_string.endswith('\\]'):
            inner_content = latex_string[2:-2]
            return f'\\begin{{gather*}}\n\\displaystyle {inner_content}\n\\end{{gather*}}'
        # If it's in $$...$$ mode, convert to gather* for consistency
        elif latex_string.startswith('$$') and latex_string.endswith('$$'):
            inner_content = latex_string[2:-2]
            return f'\\begin{{gather*}}\n\\displaystyle {inner_content}\n\\end{{gather*}}'
    
    return latex_string


def render_latex_to_image(latex_string: str, output_path: str, dpi: int = 300, font_size: int = 24,
                         crop_to_content: bool = True) -> bool:
    """
    使用 xelatex 将包含中英文和公式的 LaTeX 字符串渲染成高质量的图片。

    这个函数封装了创建 .tex 文件、调用 xelatex 编译、
    将 PDF 转换为图片以及清理临时文件的完整流程。

    ✨ 分数渲染优化 (v2.0):
    - 自动检测分数命令 (\\frac, \\dfrac, \\tfrac, \\cfrac, \\binom等)
    - 使用 gather* 环境和 \\displaystyle 确保分数正确显示为垂直结构
    - 修复了 standalone 文档类的分数水平显示问题
    - 支持多种输入格式的自动转换

    Args:
        latex_string (str): 需要渲染的 LaTeX 字符串。支持以下格式：
            - 裸分数: "\\frac{a}{b}"
            - 内联数学模式: "$\\frac{a}{b}$"
            - 显示数学模式: "\\[\\frac{a}{b}\\]"
            - 双美元符号: "$$\\frac{a}{b}$$"
        output_path (str): 输出图片的完整路径 (例如 'output/formula.png')。
        dpi (int): 渲染图片的DPI，值越高图片越清晰。默认300DPI（优化后的平衡值）。
        font_size (int): LaTeX文档的基础字体大小(pt)，影响公式显示大小。默认24pt（优化后的大字体）。
            常用值：16(小), 20(标准), 24(大), 30(很大), 36(超大)
        crop_to_content (bool): 是否裁剪图片到内容区域，去除多余空白。默认True。

    Returns:
        bool: 如果成功渲染则返回 True，否则返回 False。

    Examples:
        >>> # 基本分数渲染
        >>> render_latex_to_image(r"\\frac{1.4y-1.2x}{1+1.2x}", "fraction.png")
        True

        >>> # 大字体渲染
        >>> render_latex_to_image(r"\\frac{a}{b}", "large_fraction.png", font_size=24)
        True

        >>> # 自定义DPI + 字体
        >>> render_latex_to_image(r"\\sum_{n=1}^{\\infty} \\frac{1}{n^2}", "series.png",
        ...                      dpi=450, font_size=16)
        True

    Note:
        - 分数会自动使用垂直布局，无需手动添加 \\displaystyle
        - 支持嵌套分数和复杂数学表达式
        - 保持对非分数表达式的完全兼容性
    """
    # 1. 定义 LaTeX 文档模板
    # ★★★ 分数渲染优化模板 (v2.1) ★★★
    # - 使用 ctex 宏包提供中文支持，需要 xelatex 引擎
    # - 移除了 xfrac 宏包以避免分数渲染冲突
    # - 保留核心数学宏包确保完整的数学符号支持
    # - standalone 文档类配合 gather* 环境确保分数垂直显示
    # - 支持动态字体大小控制，改善公式显示大小
    # - 使用紧凑边框(1pt)减少空白区域
    # - 使用fontsize命令确保字体大小生效
    latex_template = r"""
    \documentclass[border=1pt]{standalone}
    \usepackage{ctex}
    \usepackage{amsmath}
    \usepackage{amsfonts}
    \usepackage{amssymb}
    \usepackage{mathtools}
    \begin{document}
    \fontsize{%d}{%d}\selectfont
    %s
    \end{document}
    """
    
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        tex_path = temp_path / "formula.tex"
        pdf_path = temp_path / "formula.pdf"
        log_path = temp_path / "formula.log"
        
        # 智能处理数学模式
        processed_latex = _process_latex_string(latex_string)

        # 使用动态字体大小生成LaTeX代码
        # 计算行间距（通常是字体大小的1.2倍）
        line_spacing = int(font_size * 1.2)
        full_latex_code = latex_template % (font_size, line_spacing, processed_latex)
        with open(tex_path, "w", encoding='utf-8') as f:
            f.write(full_latex_code)
            
        try:
            # ★★★ 关键改动: 使用 'xelatex' 引擎并强制使用 'utf-8' 编码 ★★★
            process = subprocess.run(
                ["xelatex", "-interaction=nonstopmode", "formula.tex"],
                capture_output=True, 
                text=True,           
                timeout=60, 
                cwd=temp_dir,
                encoding='utf-8', # 明确指定使用 utf-8 读取子进程的输出
                errors='ignore'   # 忽略任何剩余的编码错误
            )
        except FileNotFoundError:
            print("❌ 错误: 'xelatex' 命令未找到。请确保你的 MiKTeX/TeX Live 发行版已完整安装。")
            return False
        except subprocess.TimeoutExpired:
            print(f"❌ 渲染超时: LaTeX 编译时间超过 60 秒，可能是语法非常复杂或有错误。")
            return False

        if not pdf_path.exists():
            print(f"❌ 渲染失败: PDF 文件未能生成。请检查你的 LaTeX 字符串语法是否有误: '{latex_string}'")
            if log_path.exists():
                print("\n--- LaTeX 编译日志 (formula.log) ---")
                with open(log_path, "r", encoding='utf-8', errors='ignore') as log_file:
                    print(log_file.read())
                print("--- 日志结束 ---")
            else:
                print("\n--- 来自 xelatex 的原始输出 (stdout) ---")
                print(process.stdout)
                print("\n--- 来自 xelatex 的原始输出 (stderr) ---")
                print(process.stderr)
            return False
            
        try:
            # 增加 timeout 参数到60秒，以处理复杂公式
            images = pdf2image.convert_from_path(
                pdf_path,
                dpi=dpi,
                fmt='png',
                transparent=True,
                timeout=60
            )
            if images:
                image = images[0]

                # 如果启用内容裁剪，去除多余的空白区域
                if crop_to_content:
                    image = _crop_to_content(image)

                image.save(output_path, 'PNG')
            else:
                print(f"❌ 渲染失败: PDF 已生成但 pdf2image 未能转换它。")
                return False
        except Exception as e:
            # 即使PDF存在但转换失败，也要打印日志来诊断问题
            print(f"❌ 渲染失败: 从 PDF 转换到图片时出错。错误: {e}")
            if log_path.exists():
                print("\n--- LaTeX 编译日志 (formula.log) ---")
                print("💡 PDF文件已生成，但可能已损坏。以下是编译日志，请检查其中的错误信息：")
                with open(log_path, "r", encoding='utf-8', errors='ignore') as log_file:
                    print(log_file.read())
                print("--- 日志结束 ---")
            else:
                 print("💡 提示: 请确保 Poppler 已被正确安装并加入系统 PATH。")
            return False
            
    return True


# ----------------------------------------------------------------------------
# 用于并排对比的辅助函数
# ----------------------------------------------------------------------------
def compare_image_with_rendered_latex(original_image_path: str, latex_string: str):
    """
    在一个窗口中上下对比显示原始图像、渲染后的 LaTeX 公式以及原始字符串。

    交互说明：
    - 按空格键：进入下一个LaTeX字符串的渲染和比较
    - 按ESC键：退出渲染程序

    Returns:
        str: 用户操作类型 ('next' 表示继续下一个, 'quit' 表示退出程序)
    """
    # 解决中文乱码问题
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    rendered_image_path = Path("temp_rendered_formula.png")
    
    # print("正在渲染 LaTeX 字符串...")
    success = render_latex_to_image(latex_string, str(rendered_image_path))
    
    fig = plt.figure(figsize=(8, 10))
    gs = gridspec.GridSpec(3, 1, height_ratios=[4, 4, 1])
    
    ax1 = fig.add_subplot(gs[0])
    ax2 = fig.add_subplot(gs[1])
    ax3 = fig.add_subplot(gs[2])

    # 用于存储用户操作的变量
    user_action = {'action': 'next'}  # 默认为继续下一个

    # 添加按键交互功能
    def on_key_press(event):
        if event.key == ' ':  # 空格键：进入下一个
            user_action['action'] = 'next'
            plt.close(event.canvas.figure)
        elif event.key == 'escape':  # ESC键：退出程序
            user_action['action'] = 'quit'
            plt.close(event.canvas.figure)

    fig.canvas.mpl_connect('key_press_event', on_key_press)
    
    fig.suptitle("原始图像 vs. 渲染结果 (上下对比)", fontsize=16)

    # 上方的子图：原始图像
    ax1.set_title('原始图像')
    ax1.axis('off')
    try:
        original_img = mpimg.imread(original_image_path)
        ax1.imshow(original_img)
    except FileNotFoundError:
        ax1.text(0.5, 0.5, '原始图像\n未找到', ha='center', va='center', color='red')

    # 中间的子图：渲染结果
    ax2.set_title('渲染结果 (via xelatex)')
    ax2.axis('off')
    if success and rendered_image_path.exists():
        rendered_img = mpimg.imread(str(rendered_image_path))
        ax2.imshow(rendered_img)
        os.remove(rendered_image_path)
    else:
        ax2.text(0.5, 0.5, 'LaTeX 渲染失败!', ha='center', va='center', color='red')

    # 最下方的子图，用于显示原始 LaTeX 字符串
    ax3.set_title('输入的 LaTeX 字符串')
    ax3.axis('off')

    # 安全显示LaTeX字符串，避免matplotlib mathtext解析错误
    # 使用最安全的方法：完全避免LaTeX解析
    try:
        # 对于复杂的LaTeX字符串，截断显示以避免界面过于拥挤
        display_string = latex_string
        if len(latex_string) > 150:
            display_string = latex_string[:150] + "..."

        # 替换可能导致解析问题的字符
        safe_string = display_string.replace('$', '\\$').replace('\\', '\\\\')

        # 使用最安全的文本显示方式
        ax3.text(0.5, 0.5, safe_string, ha='center', va='center',
                fontsize=8, wrap=True, family='monospace')

    except Exception:
        # 如果仍然出错，使用最基本的显示方式
        try:
            # 只显示字符串的基本信息，不显示内容
            info_msg = f"LaTeX字符串 (长度: {len(latex_string)} 字符)\n[内容过于复杂，无法安全显示]"
            ax3.text(0.5, 0.5, info_msg, ha='center', va='center',
                    fontsize=10, color='blue')
        except Exception:
            # 最后的备选方案：显示固定文本
            ax3.text(0.5, 0.5, "LaTeX字符串\n(显示被禁用)", ha='center', va='center',
                    fontsize=10, color='gray')

    plt.tight_layout(rect=[0, 0, 1, 0.96])

    # 添加操作提示
    fig.text(0.5, 0.02, '按空格键继续下一个 | 按ESC键退出程序',
             ha='center', va='bottom', fontsize=10, style='italic')

    plt.show()

    # 返回用户操作
    return user_action['action']


# ----------------------------------------------------------------------------
# 脚本主入口
# ----------------------------------------------------------------------------
if __name__ == '__main__':
    # --- 使用示例 ---
    
    pred_latex_txt = r"E:\codes\OCR\MSR\msr\pred_texteller_3-dep.txt"
    with open(pred_latex_txt, "r", encoding="utf-8") as fr:
        pred_latex = fr.readlines()

    root_path = r"D:\datasets\MSR\formula_set_select2"
    img_list = os.listdir(root_path)
    img_list.sort()

    print("LaTeX渲染对比程序启动")
    print("交互说明：")
    print("  - 按空格键：进入下一个LaTeX字符串的渲染和比较")
    print("  - 按ESC键：退出渲染程序")
    print("-" * 50)

    for idx, imname in enumerate(img_list):
        if idx < 180:
            continue
        print(f"正在处理第 {idx+1}/{len(img_list)} 个图片: {imname}")

        # 调用对比函数并获取用户操作
        action = compare_image_with_rendered_latex(os.path.join(root_path, imname), pred_latex[idx].strip().replace("\if", "if"))

        if action == 'quit':
            print("用户选择退出程序")
            break
        elif action == 'next':
            # print("继续下一个...")
            continue

    print("程序结束")
