#!/usr/bin/env python3
import sys, os
sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

try:
    from render_image import render_latex_to_image
    print("测试: a+b=c \\tag{2}")
    success = render_latex_to_image("a+b=c \\tag{2}", "quick_tag_test.png")
    if success:
        print("✅ 成功: quick_tag_test.png")
        print("请检查图片中标签(2)的位置是否正确")
    else:
        print("❌ 失败")
except Exception as e:
    print(f"错误: {e}")
