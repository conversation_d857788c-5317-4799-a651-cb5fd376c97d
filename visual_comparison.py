#!/usr/bin/env python3
"""
可视化对比脚本：查看不同配置下的分数渲染效果
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import os
import sys
from pathlib import Path
import tempfile
import subprocess
import pdf2image

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def create_test_latex_documents():
    """创建不同配置的LaTeX文档进行对比"""
    
    # 测试用例
    frac_expr = r"\frac{1.4y-1.2x}{1+1.2x}"
    
    # 不同的LaTeX配置
    configs = {
        "标准配置": {
            "template": r"""
\documentclass[border=2pt]{standalone}
\usepackage{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\usepackage{xfrac}
\begin{document}
\[\frac{1.4y-1.2x}{1+1.2x}\]
\end{document}
""",
            "filename": "standard_config.png"
        },
        
        "移除xfrac": {
            "template": r"""
\documentclass[border=2pt]{standalone}
\usepackage{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\begin{document}
\[\frac{1.4y-1.2x}{1+1.2x}\]
\end{document}
""",
            "filename": "no_xfrac.png"
        },
        
        "内联模式": {
            "template": r"""
\documentclass[border=2pt]{standalone}
\usepackage{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\usepackage{xfrac}
\begin{document}
$\frac{1.4y-1.2x}{1+1.2x}$
\end{document}
""",
            "filename": "inline_mode.png"
        },
        
        "强制显示样式": {
            "template": r"""
\documentclass[border=2pt]{standalone}
\usepackage{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\usepackage{xfrac}
\begin{document}
\[\displaystyle\frac{1.4y-1.2x}{1+1.2x}\]
\end{document}
""",
            "filename": "displaystyle.png"
        },
        
        "使用dfrac": {
            "template": r"""
\documentclass[border=2pt]{standalone}
\usepackage{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\usepackage{xfrac}
\begin{document}
\[\dfrac{1.4y-1.2x}{1+1.2x}\]
\end{document}
""",
            "filename": "dfrac.png"
        },
        
        "使用tfrac": {
            "template": r"""
\documentclass[border=2pt]{standalone}
\usepackage{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\usepackage{xfrac}
\begin{document}
\[\tfrac{1.4y-1.2x}{1+1.2x}\]
\end{document}
""",
            "filename": "tfrac.png"
        }
    }
    
    results = {}
    
    for config_name, config_data in configs.items():
        print(f"生成 {config_name} 配置的图片...")
        success = render_latex_direct(config_data["template"], config_data["filename"])
        results[config_name] = {
            "success": success,
            "filename": config_data["filename"]
        }
        print(f"  {'✅ 成功' if success else '❌ 失败'}")
    
    return results

def render_latex_direct(latex_code, output_path, dpi=300):
    """直接渲染LaTeX代码"""
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        tex_path = temp_path / "test.tex"
        pdf_path = temp_path / "test.pdf"
        
        with open(tex_path, "w", encoding='utf-8') as f:
            f.write(latex_code)
        
        try:
            process = subprocess.run(
                ["xelatex", "-interaction=nonstopmode", "test.tex"],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=temp_dir,
                encoding='utf-8',
                errors='ignore'
            )
            
            if not pdf_path.exists():
                print(f"  PDF生成失败")
                return False
            
            images = pdf2image.convert_from_path(
                pdf_path,
                dpi=dpi,
                fmt='png',
                transparent=True,
                timeout=60
            )
            
            if images:
                images[0].save(output_path, 'PNG')
                return True
            else:
                print("  PDF转图片失败")
                return False
                
        except Exception as e:
            print(f"  渲染过程出错: {e}")
            return False

def display_comparison(results):
    """显示对比结果"""
    successful_results = {k: v for k, v in results.items() if v["success"]}
    
    if not successful_results:
        print("没有成功的渲染结果可以显示")
        return
    
    # 创建子图
    n_results = len(successful_results)
    cols = 3
    rows = (n_results + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
    if rows == 1:
        axes = axes.reshape(1, -1)
    elif cols == 1:
        axes = axes.reshape(-1, 1)
    
    # 解决中文显示问题
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    for i, (config_name, result_data) in enumerate(successful_results.items()):
        row = i // cols
        col = i % cols
        
        try:
            img = mpimg.imread(result_data["filename"])
            axes[row, col].imshow(img)
            axes[row, col].set_title(config_name, fontsize=12)
            axes[row, col].axis('off')
        except Exception as e:
            axes[row, col].text(0.5, 0.5, f'加载失败\n{e}', 
                              ha='center', va='center', transform=axes[row, col].transAxes)
            axes[row, col].set_title(config_name, fontsize=12)
            axes[row, col].axis('off')
    
    # 隐藏多余的子图
    for i in range(n_results, rows * cols):
        row = i // cols
        col = i % cols
        axes[row, col].axis('off')
    
    plt.suptitle('LaTeX分数渲染对比 - 不同配置效果', fontsize=16)
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    print("开始生成不同配置的LaTeX分数渲染对比...")
    
    # 生成测试图片
    results = create_test_latex_documents()
    
    # 显示对比结果
    print("\n显示对比结果...")
    display_comparison(results)
    
    # 打印分析结果
    print("\n=== 分析结果 ===")
    for config_name, result_data in results.items():
        if result_data["success"]:
            print(f"✅ {config_name}: 渲染成功，文件: {result_data['filename']}")
        else:
            print(f"❌ {config_name}: 渲染失败")

if __name__ == "__main__":
    main()
