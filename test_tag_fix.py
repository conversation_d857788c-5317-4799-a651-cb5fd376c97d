#!/usr/bin/env python3
"""
测试\tag命令修复效果
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_tag_fix():
    """测试\tag命令修复效果"""
    print("=== 测试\\tag命令修复效果 ===")
    
    # 之前失败的测试用例
    fixed_test_cases = [
        {
            "name": "裸公式+tag（修复前失败）",
            "latex": r"E = mc^2 \tag{<PERSON>}",
            "description": "裸公式包含tag，应该使用gather环境"
        },
        {
            "name": "内联数学+tag",
            "latex": r"$F = ma \tag{牛顿第二定律}$",
            "description": "内联数学模式包含tag"
        },
        {
            "name": "显示数学+tag",
            "latex": r"\[a^2 + b^2 = c^2 \tag{勾股定理}\]",
            "description": "显示数学模式包含tag"
        },
        {
            "name": "双美元符号+tag",
            "latex": r"$$\int_0^1 f(x) dx = F(1) - F(0) \tag{微积分基本定理}$$",
            "description": "双美元符号包含tag"
        },
        {
            "name": "分数+tag组合",
            "latex": r"\frac{dy}{dx} = \lim_{h \to 0} \frac{f(x+h) - f(x)}{h} \tag{导数定义}",
            "description": "分数和tag的组合"
        },
        {
            "name": "复杂公式+tag",
            "latex": r"\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6} \tag{巴塞尔问题}",
            "description": "复杂数学表达式+tag"
        }
    ]
    
    try:
        from render_image import render_latex_to_image, _process_latex_string
        
        print("\n🔍 测试LaTeX字符串处理逻辑:")
        
        for i, test_case in enumerate(fixed_test_cases, 1):
            name = test_case["name"]
            latex = test_case["latex"]
            description = test_case["description"]
            
            print(f"\n测试 {i}: {name}")
            print(f"描述: {description}")
            print(f"原始: {latex}")
            
            # 测试处理逻辑
            try:
                processed = _process_latex_string(latex)
                print(f"处理后: {processed}")
                
                # 检查是否使用了正确的环境
                if '\\tag' in latex:
                    if 'gather}' in processed and 'gather*}' not in processed:
                        print(f"  ✅ 正确使用gather环境（支持\\tag）")
                    elif 'gather*}' in processed:
                        print(f"  ❌ 错误使用gather*环境（不支持\\tag）")
                    else:
                        print(f"  ⚠️  使用其他环境")
                
            except Exception as e:
                print(f"  ❌ 处理出错: {e}")
        
        print(f"\n🧪 测试实际渲染:")
        
        results = {}
        
        for i, test_case in enumerate(fixed_test_cases, 1):
            name = test_case["name"]
            latex = test_case["latex"]
            
            print(f"\n渲染测试 {i}: {name}")
            
            output_file = f"tag_fix_test_{i}_{name.replace(' ', '_').replace('（', '').replace('）', '')}.png"
            
            try:
                success = render_latex_to_image(latex, output_file)
                
                if success:
                    print(f"  ✅ 渲染成功: {output_file}")
                    results[name] = "成功"
                else:
                    print(f"  ❌ 渲染失败")
                    results[name] = "失败"
                    
            except Exception as e:
                print(f"  ❌ 渲染出错: {str(e)[:100]}...")
                results[name] = f"错误: {str(e)[:50]}..."
        
        return results
        
    except ImportError as e:
        print(f"❌ 无法导入模块: {e}")
        return {}

def test_edge_cases():
    """测试边缘情况"""
    print("\n=== 测试边缘情况 ===")
    
    edge_cases = [
        {
            "name": "多个tag",
            "latex": r"\begin{gather} a = b \tag{1} \\ c = d \tag{2} \end{gather}",
            "description": "多行公式，每行都有tag"
        },
        {
            "name": "tag中包含数学",
            "latex": r"E = mc^2 \tag{$E_0$}",
            "description": "tag内容包含数学符号"
        },
        {
            "name": "空tag",
            "latex": r"F = ma \tag{}",
            "description": "空的tag"
        },
        {
            "name": "tag和其他命令",
            "latex": r"\frac{a}{b} = c \tag{分数} \quad \text{其中} \quad a > 0",
            "description": "tag和其他LaTeX命令混合"
        }
    ]
    
    try:
        from render_image import render_latex_to_image
        
        results = {}
        
        for i, test_case in enumerate(edge_cases, 1):
            name = test_case["name"]
            latex = test_case["latex"]
            description = test_case["description"]
            
            print(f"\n边缘测试 {i}: {name}")
            print(f"描述: {description}")
            print(f"LaTeX: {latex}")
            
            output_file = f"edge_case_{i}_{name.replace(' ', '_')}.png"
            
            try:
                success = render_latex_to_image(latex, output_file)
                
                if success:
                    print(f"  ✅ 渲染成功: {output_file}")
                    results[name] = "成功"
                else:
                    print(f"  ❌ 渲染失败")
                    results[name] = "失败"
                    
            except Exception as e:
                print(f"  ❌ 渲染出错: {str(e)[:100]}...")
                results[name] = f"错误: {str(e)[:50]}..."
        
        return results
        
    except Exception as e:
        print(f"❌ 边缘测试失败: {e}")
        return {}

def main():
    """主函数"""
    print("LaTeX \\tag命令修复验证")
    print("=" * 50)
    
    print("\n🎯 修复内容:")
    print("  问题: \\tag命令在gather*环境中不被允许")
    print("  解决: 检测\\tag命令，自动使用gather环境（支持\\tag）")
    print("  影响: 包含\\tag的公式现在应该能正常渲染")
    
    print("\n🔧 修复逻辑:")
    print("  1. 检测LaTeX字符串中是否包含\\tag命令")
    print("  2. 如果包含\\tag，使用gather环境而不是gather*环境")
    print("  3. 保持其他功能不变（分数垂直显示等）")
    
    # 测试修复效果
    print("\n🧪 开始修复验证...")
    main_results = test_tag_fix()
    
    # 测试边缘情况
    edge_results = test_edge_cases()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("🎯 修复验证结果")
    print("=" * 50)
    
    all_results = {**main_results, **edge_results}
    
    if all_results:
        success_count = sum(1 for result in all_results.values() if result == "成功")
        total_count = len(all_results)
        
        print(f"\n📊 总体测试结果:")
        print(f"  总测试数: {total_count}")
        print(f"  成功数: {success_count}")
        print(f"  失败数: {total_count - success_count}")
        print(f"  成功率: {success_count/total_count*100:.1f}%")
        
        print(f"\n📋 详细结果:")
        for name, result in all_results.items():
            status = "✅" if result == "成功" else "❌"
            print(f"  {status} {name}: {result}")
        
        if success_count == total_count:
            print(f"\n🎉 \\tag命令修复完全成功！")
            print(f"所有测试用例都通过了")
        elif success_count > total_count * 0.8:
            print(f"\n✅ \\tag命令修复基本成功")
            print(f"大部分测试用例通过，少数边缘情况可能需要进一步优化")
        else:
            print(f"\n⚠️  \\tag命令修复部分成功")
            print(f"建议检查失败的测试用例")
    else:
        print(f"\n❌ 无法运行测试")
    
    print(f"\n💡 使用建议:")
    print(f"  现在您可以在LaTeX字符串中正常使用\\tag命令了")
    print(f"  支持的格式:")
    print(f"    • E = mc^2 \\tag{{标签}}")
    print(f"    • $公式 \\tag{{标签}}$")
    print(f"    • \\[公式 \\tag{{标签}}\\]")
    print(f"    • $$公式 \\tag{{标签}}$$")

if __name__ == "__main__":
    main()
