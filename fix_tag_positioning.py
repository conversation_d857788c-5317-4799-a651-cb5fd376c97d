#!/usr/bin/env python3
"""
修复\tag标签位置问题
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_tag_positioning():
    """测试\tag标签位置"""
    print("=== 测试\\tag标签位置修复 ===")
    
    # 创建不同的LaTeX测试来验证正确的\tag行为
    test_cases = [
        {
            "name": "标准equation环境",
            "latex": r"""
\documentclass[border=1pt]{standalone}
\usepackage{amsmath}
\begin{document}
\begin{equation}
a + b = c \tag{2}
\end{equation}
\end{document}
""",
            "file": "test_standard_equation.tex",
            "description": "标准equation环境，公式居中，标签在右侧"
        },
        {
            "name": "无displaystyle的equation",
            "latex": r"""
\documentclass[border=1pt]{standalone}
\usepackage{amsmath}
\begin{document}
\begin{equation}
a + b = c \tag{2}
\end{equation}
\end{document}
""",
            "file": "test_no_displaystyle.tex",
            "description": "不使用\\displaystyle，避免干扰标签位置"
        },
        {
            "name": "带字体设置的equation",
            "latex": r"""
\documentclass[border=1pt]{standalone}
\usepackage{amsmath}
\begin{document}
\fontsize{24}{29}\selectfont
\begin{equation}
a + b = c \tag{2}
\end{equation}
\end{document}
""",
            "file": "test_fontsize_equation.tex",
            "description": "带字体设置的equation环境"
        },
        {
            "name": "align环境测试",
            "latex": r"""
\documentclass[border=1pt]{standalone}
\usepackage{amsmath}
\begin{document}
\fontsize{24}{29}\selectfont
\begin{align}
a + b = c \tag{2}
\end{align}
\end{document}
""",
            "file": "test_align_env.tex",
            "description": "使用align环境测试标签位置"
        }
    ]
    
    print("🔧 创建测试文件...")
    
    for test_case in test_cases:
        name = test_case["name"]
        latex_content = test_case["latex"]
        filename = test_case["file"]
        description = test_case["description"]
        
        print(f"\n创建 {name}: {filename}")
        print(f"  描述: {description}")
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(latex_content)
        
        print(f"  ✅ 已保存: {filename}")
        print(f"  💡 编译命令: xelatex {filename}")
    
    return test_cases

def analyze_current_issue():
    """分析当前问题"""
    print("\n=== 分析当前问题 ===")
    
    print("🚨 问题描述:")
    print("  当前渲染: a + b = c(2)  ❌ (标签紧贴公式)")
    print("  正确效果: a + b = c     (2)  ✅ (公式居中，标签在右侧)")
    
    print("\n🔍 可能的原因:")
    print("  1. \\displaystyle 可能影响标签位置")
    print("  2. equation环境的标签渲染可能有问题")
    print("  3. 字体设置可能干扰标签定位")
    print("  4. standalone文档类可能影响标签布局")
    
    print("\n💡 解决方案:")
    print("  1. 移除\\displaystyle（equation环境默认就是display模式）")
    print("  2. 确保使用正确的数学环境")
    print("  3. 调整字体设置方式")
    print("  4. 测试不同的文档类选项")

def suggest_code_fixes():
    """建议代码修复"""
    print("\n=== 建议代码修复 ===")
    
    print("🔧 需要修改的地方:")
    
    print("\n1. **移除\\displaystyle**:")
    print("   当前: \\begin{equation}\\n\\displaystyle a+b=c \\tag{2}\\n\\end{equation}")
    print("   修改: \\begin{equation}\\na+b=c \\tag{2}\\n\\end{equation}")
    print("   原因: equation环境本身就是display模式，额外的\\displaystyle可能干扰标签")
    
    print("\n2. **检查环境选择**:")
    print("   equation: 单行公式，自动编号，支持\\tag")
    print("   align: 多行对齐，支持\\tag")
    print("   gather: 多行居中，支持\\tag")
    
    print("\n3. **字体设置优化**:")
    print("   确保字体设置不干扰数学环境的布局")

def main():
    """主函数"""
    print("LaTeX \\tag标签位置修复")
    print("=" * 50)
    
    print("\n🎯 目标:")
    print("  修复\\tag{2}的位置，让公式居中，标签在右侧")
    
    # 分析问题
    analyze_current_issue()
    
    # 创建测试文件
    test_cases = test_tag_positioning()
    
    # 建议修复
    suggest_code_fixes()
    
    print("\n" + "=" * 50)
    print("🎯 下一步行动")
    print("=" * 50)
    
    print("\n📋 验证步骤:")
    print("  1. 手动编译测试文件，查看哪个显示正确")
    print("  2. 找到正确的LaTeX模式后，修改代码")
    print("  3. 重新测试渲染效果")
    
    print("\n🔧 预期修复:")
    print("  修改_process_latex_string函数，移除\\displaystyle")
    print("  确保\\tag命令在正确的环境中工作")
    
    print(f"\n📁 测试文件:")
    for test_case in test_cases:
        print(f"  • {test_case['file']} - {test_case['description']}")

if __name__ == "__main__":
    main()
