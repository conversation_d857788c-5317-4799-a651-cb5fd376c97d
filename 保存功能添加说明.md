# 保存对比图像功能添加说明

## 🎯 功能概述

为LaTeX渲染对比程序添加了保存功能，用户现在可以通过按 **S键** 将当前的对比图像保存到本地。

## 🔧 实现细节

### 新增按键功能
- **S键**：保存当前对比图像到 `output/` 文件夹
- **空格键**：进入下一个LaTeX字符串（原有功能）
- **ESC键**：退出程序（原有功能）

### 保存功能特点

#### 1. 自动文件管理
```python
# 自动创建output文件夹
output_dir = Path("output")
output_dir.mkdir(exist_ok=True)

# 智能文件命名
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
output_filename = f"{safe_name}_{timestamp}_comparison.png"
```

#### 2. 高质量保存
```python
# 300DPI高质量PNG格式
fig.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
```

#### 3. 安全文件名处理
```python
# 移除特殊字符，确保文件名安全
safe_name = "".join(c for c in original_name if c.isalnum() or c in ('-', '_'))
```

#### 4. 完善的错误处理
```python
try:
    # 保存操作
    fig.savefig(...)
    print(f"📁 保存路径: {output_path}")
except Exception as e:
    print(f"❌ 保存失败: {e}")
```

## 📁 保存的文件内容

保存的图像包含完整的对比布局：

1. **原始图像**（上方）
   - 用户提供的原始公式图片
   - 标题："原始图像"

2. **渲染结果**（中间）
   - xelatex渲染的LaTeX公式
   - 标题："渲染结果 (via xelatex)"

3. **LaTeX字符串**（下方）
   - 显示原始的LaTeX代码
   - 标题："输入的 LaTeX 字符串"

4. **界面元素**
   - 主标题："原始图像 vs. 渲染结果 (上下对比)"
   - 操作提示："按空格键继续下一个 | 按ESC键退出程序 | 按S键保存对比图像"

## 🎮 使用方法

### 启动程序
```bash
python msr/tools/render_image.py
```

### 操作流程
1. 程序显示对比窗口
2. 查看原始图像和渲染结果的对比
3. 按 **S键** 保存当前对比图像
4. 控制台显示保存信息
5. 按空格键继续下一个或ESC键退出

### 控制台反馈示例
```
✅ 对比图像已保存到 output 文件夹
📁 保存路径: output\formula_001_20250724_115444_comparison.png
📊 图像信息: formula_001 - 85 字符LaTeX
```

## 📝 文件命名规则

### 命名格式
```
{原图名}_{时间戳}_comparison.png
```

### 示例
```
formula_001_20250724_115444_comparison.png
complex_equation_20250724_120530_comparison.png
test_image_20250724_121205_comparison.png
```

### 命名说明
- **原图名**：从原始图片文件名提取，移除特殊字符
- **时间戳**：YYYYMMDD_HHMMSS格式，确保文件不重复
- **后缀**：固定为 `_comparison.png`

## 🔧 代码修改详情

### 1. 新增保存函数
```python
def save_comparison_image(fig, original_image_path: str, latex_string: str):
    """保存对比图像到output文件夹"""
    # 创建output文件夹
    # 生成安全文件名
    # 保存高质量图像
    # 提供用户反馈
```

### 2. 修改按键处理
```python
def on_key_press(event):
    if event.key == ' ':  # 空格键：进入下一个
        user_action['action'] = 'next'
        plt.close(event.canvas.figure)
    elif event.key == 'escape':  # ESC键：退出程序
        user_action['action'] = 'quit'
        plt.close(event.canvas.figure)
    elif event.key == 's':  # S键：保存对比图像（新增）
        save_comparison_image(fig, original_image_path, latex_string)
        print("✅ 对比图像已保存到 output 文件夹")
```

### 3. 更新用户界面
```python
# 更新操作提示
fig.text(0.5, 0.02, '按空格键继续下一个 | 按ESC键退出程序 | 按S键保存对比图像', 
         ha='center', va='bottom', fontsize=10, style='italic')

# 更新启动说明
print("  - 按S键：保存当前对比图像到output文件夹")
```

## ✅ 功能验证

### 测试结果
- ✅ 保存函数基础测试通过
- ✅ 文件正确保存到output文件夹
- ✅ 文件命名规则正确执行
- ✅ 高质量图像保存（300DPI）
- ✅ 错误处理机制正常
- ✅ 用户反馈信息准确

### 测试文件
- `test_save_function.py` - 保存功能测试脚本
- `demo_save_feature.py` - 保存功能演示脚本

## 🎉 用户收益

### 便利性提升
- **一键保存**：按S键即可保存当前对比
- **自动管理**：无需手动创建文件夹或命名
- **批量保存**：可以连续保存多个对比图像

### 质量保证
- **高分辨率**：300DPI确保打印质量
- **完整布局**：保存包含所有对比信息
- **标准格式**：PNG格式兼容性好

### 工作流程优化
- **文档制作**：可直接用于报告和论文
- **质量检查**：保存有问题的对比进行分析
- **结果分享**：方便与他人分享对比结果

## 🔮 未来扩展可能

### 可能的增强功能
1. **保存格式选择**：支持PDF、SVG等格式
2. **批量保存**：一键保存所有对比图像
3. **自定义保存路径**：允许用户指定保存位置
4. **图像质量设置**：可调节DPI和压缩质量
5. **元数据记录**：保存LaTeX源码到文件

### 配置选项
```python
# 未来可能的配置选项
SAVE_CONFIG = {
    'format': 'png',  # 'png', 'pdf', 'svg'
    'dpi': 300,       # 图像分辨率
    'quality': 95,    # 压缩质量
    'output_dir': 'output',  # 保存目录
    'include_metadata': True  # 是否包含元数据
}
```

## 📋 总结

保存功能的成功添加为LaTeX渲染对比程序带来了重要的实用价值：

- **功能完整**：实现了完整的保存工作流程
- **用户友好**：简单的按键操作，清晰的反馈信息
- **质量可靠**：高质量图像保存，完善的错误处理
- **扩展性好**：代码结构清晰，便于未来功能扩展

现在用户可以方便地保存任何感兴趣的对比图像，大大提升了程序的实用性和工作效率！
