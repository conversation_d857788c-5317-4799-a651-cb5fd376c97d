#!/usr/bin/env python3
"""
查看测试结果的脚本
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import os

def view_test_images():
    """查看测试生成的图片"""
    
    # 查找所有测试生成的图片
    test_images = []
    for file in os.listdir('.'):
        if file.startswith('test_') and file.endswith('.png'):
            test_images.append(file)
    
    if not test_images:
        print("没有找到测试图片")
        return
    
    # 创建子图显示所有测试结果
    fig, axes = plt.subplots(len(test_images), 1, figsize=(10, 3*len(test_images)))
    if len(test_images) == 1:
        axes = [axes]
    
    for i, img_file in enumerate(test_images):
        try:
            img = mpimg.imread(img_file)
            axes[i].imshow(img)
            axes[i].set_title(f'{img_file}')
            axes[i].axis('off')
        except Exception as e:
            axes[i].text(0.5, 0.5, f'无法加载 {img_file}\n错误: {e}', 
                        ha='center', va='center', transform=axes[i].transAxes)
            axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    view_test_images()
