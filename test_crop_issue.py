#!/usr/bin/env python3
"""
测试裁剪对\tag的影响
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_crop_impact():
    """测试裁剪对\tag的影响"""
    print("=== 测试裁剪对\\tag的影响 ===")
    
    try:
        from render_image import render_latex_to_image
        
        test_latex = "a+b=c \\tag{2}"
        
        print(f"测试LaTeX: {test_latex}")
        
        # 测试1：启用裁剪（默认）
        print(f"\n🧪 测试1: 启用裁剪（当前默认行为）")
        success1 = render_latex_to_image(
            test_latex, 
            "tag_with_crop.png", 
            crop_to_content=True
        )
        
        if success1:
            print(f"✅ 渲染成功: tag_with_crop.png")
        else:
            print(f"❌ 渲染失败")
        
        # 测试2：禁用裁剪
        print(f"\n🧪 测试2: 禁用裁剪（保留原始布局）")
        success2 = render_latex_to_image(
            test_latex, 
            "tag_without_crop.png", 
            crop_to_content=False
        )
        
        if success2:
            print(f"✅ 渲染成功: tag_without_crop.png")
        else:
            print(f"❌ 渲染失败")
        
        return success1, success2
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, False

def analyze_files():
    """分析生成的文件"""
    print("\n=== 分析生成的文件 ===")
    
    files = [
        ("tag_with_crop.png", "启用裁剪"),
        ("tag_without_crop.png", "禁用裁剪")
    ]
    
    for filename, description in files:
        if Path(filename).exists():
            file_size = Path(filename).stat().st_size
            print(f"📁 {filename} ({description}): {file_size} 字节")
        else:
            print(f"❌ {filename}: 文件不存在")

def main():
    """主函数"""
    print("LaTeX \\tag裁剪问题分析")
    print("=" * 50)
    
    print("\n🔍 问题假设:")
    print("  裁剪逻辑移除了公式和标签之间的空白")
    print("  导致 (2) 看起来紧贴在公式后面")
    
    print("\n🧪 验证方法:")
    print("  对比启用/禁用裁剪的渲染效果")
    
    # 运行测试
    success1, success2 = test_crop_impact()
    
    # 分析文件
    analyze_files()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果")
    print("=" * 50)
    
    if success1 and success2:
        print("✅ 两个测试都成功")
        print("\n📋 对比分析:")
        print("  • tag_with_crop.png - 启用裁剪（当前问题版本）")
        print("  • tag_without_crop.png - 禁用裁剪（可能的正确版本）")
        
        print("\n💡 预期结果:")
        print("  如果禁用裁剪的版本显示正确的标签位置，")
        print("  说明问题确实是由裁剪逻辑导致的")
    else:
        print("❌ 测试失败")
    
    print(f"\n🔧 如果验证了裁剪问题:")
    print(f"  需要修改裁剪逻辑，保留\\tag标签的正确间距")

if __name__ == "__main__":
    main()
