#!/usr/bin/env python3
"""
测试\tag位置修复
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_tag_position_fix():
    """测试\tag位置修复"""
    print("=== 测试\\tag位置修复 ===")
    
    try:
        from render_image import render_latex_to_image, _process_latex_string
        
        test_latex = "a+b=c \\tag{2}"
        
        print(f"测试LaTeX: {test_latex}")
        
        # 检查处理逻辑
        processed = _process_latex_string(test_latex)
        print(f"处理后: {processed}")
        
        # 检查是否移除了\displaystyle
        if '\\displaystyle' in processed:
            print("⚠️  仍然包含\\displaystyle，可能影响标签位置")
        else:
            print("✅ 已移除\\displaystyle，标签位置应该正确")
        
        # 测试渲染
        output_file = "tag_position_fix_test.png"
        
        print(f"\n🧪 开始渲染测试...")
        success = render_latex_to_image(test_latex, output_file)
        
        if success:
            print(f"✅ 渲染成功: {output_file}")
            print("📋 请检查图片:")
            print("  • 公式 a+b=c 应该居中")
            print("  • 标签 (2) 应该在行的右侧，与公式分离")
            print("  • 不应该是 a+b=c(2) 这样紧贴的效果")
            
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"📊 文件大小: {file_size} 字节")
                return True
            else:
                print("❌ 输出文件不存在")
                return False
        else:
            print("❌ 渲染失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_comparison_test():
    """创建对比测试"""
    print("\n=== 创建对比测试 ===")
    
    # 创建一个手动的正确LaTeX文件用于对比
    correct_latex = r"""
\documentclass[border=1pt]{standalone}
\usepackage{amsmath}
\begin{document}
\fontsize{24}{29}\selectfont
\begin{equation}
a+b=c \tag{2}
\end{equation}
\end{document}
"""
    
    correct_file = "correct_tag_position.tex"
    
    with open(correct_file, 'w', encoding='utf-8') as f:
        f.write(correct_latex)
    
    print(f"✅ 正确示例已创建: {correct_file}")
    print("💡 编译命令: xelatex correct_tag_position.tex")
    print("📋 这个文件应该显示正确的标签位置")
    
    return correct_file

def main():
    """主函数"""
    print("LaTeX \\tag位置修复测试")
    print("=" * 50)
    
    print("\n🎯 修复目标:")
    print("  让\\tag{2}显示正确的位置:")
    print("  • 公式居中: a + b = c")
    print("  • 标签在右侧: (2)")
    print("  • 不是紧贴: a + b = c(2) ❌")
    
    print("\n🔧 修复内容:")
    print("  移除了所有\\tag相关处理中的\\displaystyle")
    print("  equation环境本身就是display模式，不需要额外的\\displaystyle")
    
    # 测试修复效果
    success = test_tag_position_fix()
    
    # 创建对比文件
    correct_file = create_comparison_test()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果")
    print("=" * 50)
    
    if success:
        print("✅ 渲染测试通过")
        print("\n📋 验证步骤:")
        print("  1. 查看 tag_position_fix_test.png")
        print("  2. 确认标签位置是否正确")
        print("  3. 如果需要，对比手动编译的正确示例")
        
        print(f"\n🔧 手动验证:")
        print(f"  编译: xelatex {correct_file}")
        print(f"  对比两个结果，确认标签位置一致")
    else:
        print("❌ 渲染测试失败")
    
    print(f"\n💡 期望效果:")
    print(f"  修复后应该显示:")
    print(f"    a + b = c        (2)")
    print(f"    ↑居中公式      ↑右侧标签")
    print(f"  而不是:")
    print(f"    a + b = c(2)")
    print(f"    ↑紧贴在一起")

if __name__ == "__main__":
    main()
