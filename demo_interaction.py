#!/usr/bin/env python3
"""
演示新的交互功能
"""

import sys
import os
from pathlib import Path
import matplotlib.pyplot as plt

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

from render_image import compare_image_with_rendered_latex

def create_demo_image():
    """创建演示用的图片"""
    demo_image_path = "demo_formula.png"
    
    if not Path(demo_image_path).exists():
        fig, ax = plt.subplots(figsize=(6, 3))
        ax.text(0.5, 0.5, '演示公式图片\n(Demo Formula Image)', 
                ha='center', va='center', fontsize=16, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        plt.savefig(demo_image_path, bbox_inches='tight', dpi=150)
        plt.close()
        print(f"✅ 创建演示图片: {demo_image_path}")
    
    return demo_image_path

def demo_interaction():
    """演示交互功能"""
    print("🎯 LaTeX渲染交互功能演示")
    print("=" * 50)
    
    # 创建演示图片
    demo_image = create_demo_image()
    
    # 演示用的LaTeX公式
    demo_formulas = [
        r"\frac{1.4y-1.2x}{1+1.2x}",
        r"\sum_{i=1}^{n} \frac{1}{i^2}",
        r"\int_0^1 \frac{\sin x}{x} dx",
    ]
    
    print("\n📋 演示说明:")
    print("  将显示3个LaTeX公式的渲染对比")
    print("  请测试新的按键功能：")
    print("    🔸 空格键 - 进入下一个公式")
    print("    🔸 ESC键  - 退出程序")
    print()
    
    try:
        for i, formula in enumerate(demo_formulas):
            print(f"\n📊 演示 {i+1}/{len(demo_formulas)}: {formula}")
            
            # 调用对比函数
            action = compare_image_with_rendered_latex(demo_image, formula)
            
            if action == 'quit':
                print("🚪 用户选择退出演示")
                break
            elif action == 'next':
                print("➡️  用户选择继续下一个")
                continue
        else:
            print("\n🎉 演示完成！所有公式都已展示")
    
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
    
    finally:
        # 清理演示文件
        if Path(demo_image).exists():
            os.remove(demo_image)
            print(f"🧹 清理演示文件: {demo_image}")

def main():
    """主函数"""
    print("LaTeX渲染交互功能演示程序")
    print("=" * 60)
    
    print("\n🔧 交互功能改进总结:")
    print("  ✅ 之前的问题：")
    print("     - 按ESC键进入下一个LaTeX字符串")
    print("     - 没有退出程序的方式")
    print()
    print("  ✅ 现在的解决方案：")
    print("     - 按空格键进入下一个LaTeX字符串")
    print("     - 按ESC键退出整个程序")
    print("     - 界面显示操作提示")
    print("     - 控制台显示进度和反馈")
    
    print("\n🎮 准备开始演示...")
    
    try:
        input("按回车键开始演示（Ctrl+C取消）: ")
        demo_interaction()
    except KeyboardInterrupt:
        print("\n🛑 用户取消演示")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
    
    print("\n✨ 演示程序结束")
    print("\n💡 使用提示:")
    print("  现在您可以在主程序中享受改进的交互体验：")
    print("  - 空格键：查看下一个公式")
    print("  - ESC键：随时退出程序")

if __name__ == "__main__":
    main()
