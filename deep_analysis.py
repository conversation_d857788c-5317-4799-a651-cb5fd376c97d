#!/usr/bin/env python3
"""
深度分析：找出真正导致水平分数的原因
"""

import tempfile
import subprocess
import pdf2image
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import numpy as np

def create_reference_fraction():
    """创建一个标准的垂直分数作为参考"""
    
    # 使用标准的article文档类和基本配置
    reference_latex = r"""
\documentclass{article}
\usepackage{amsmath}
\begin{document}
\pagestyle{empty}
$$\frac{1.4y-1.2x}{1+1.2x}$$
\end{document}
"""
    
    return render_latex_direct(reference_latex, "reference_vertical.png")

def test_different_document_classes():
    """测试不同文档类的效果"""
    
    frac_content = r"\frac{1.4y-1.2x}{1+1.2x}"
    
    configs = {
        "article_display": {
            "latex": f"""
\\documentclass{{article}}
\\usepackage{{amsmath}}
\\begin{{document}}
\\pagestyle{{empty}}
$${{frac_content}}$$
\\end{{document}}
""",
            "filename": "article_display.png"
        },
        
        "article_equation": {
            "latex": f"""
\\documentclass{{article}}
\\usepackage{{amsmath}}
\\begin{{document}}
\\pagestyle{{empty}}
\\begin{{equation*}}
{frac_content}
\\end{{equation*}}
\\end{{document}}
""",
            "filename": "article_equation.png"
        },
        
        "standalone_equation": {
            "latex": f"""
\\documentclass[border=2pt]{{standalone}}
\\usepackage{{amsmath}}
\\begin{{document}}
\\begin{{equation*}}
{frac_content}
\\end{{equation*}}
\\end{{document}}
""",
            "filename": "standalone_equation.png"
        },
        
        "standalone_gather": {
            "latex": f"""
\\documentclass[border=2pt]{{standalone}}
\\usepackage{{amsmath}}
\\begin{{document}}
\\begin{{gather*}}
{frac_content}
\\end{{gather*}}
\\end{{document}}
""",
            "filename": "standalone_gather.png"
        },
        
        "minimal_setup": {
            "latex": f"""
\\documentclass{{minimal}}
\\begin{{document}}
$\\displaystyle {frac_content}$
\\end{{document}}
""",
            "filename": "minimal_setup.png"
        }
    }
    
    results = {}
    
    for config_name, config_data in configs.items():
        print(f"测试 {config_name}...")
        success = render_latex_direct(config_data["latex"], config_data["filename"])
        results[config_name] = {
            "success": success,
            "filename": config_data["filename"]
        }
        print(f"  {'✅ 成功' if success else '❌ 失败'}")
    
    return results

def render_latex_direct(latex_code, output_path, dpi=300):
    """直接渲染LaTeX代码"""
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        tex_path = temp_path / "test.tex"
        pdf_path = temp_path / "test.pdf"
        log_path = temp_path / "test.log"
        
        with open(tex_path, "w", encoding='utf-8') as f:
            f.write(latex_code)
        
        try:
            process = subprocess.run(
                ["xelatex", "-interaction=nonstopmode", "test.tex"],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=temp_dir,
                encoding='utf-8',
                errors='ignore'
            )
            
            if not pdf_path.exists():
                print(f"  PDF生成失败")
                if log_path.exists():
                    with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                        log_content = f.read()
                        print(f"  LaTeX日志: {log_content[-500:]}")  # 显示最后500字符
                return False
            
            images = pdf2image.convert_from_path(
                pdf_path,
                dpi=dpi,
                fmt='png',
                transparent=True,
                timeout=60
            )
            
            if images:
                images[0].save(output_path, 'PNG')
                return True
            else:
                print("  PDF转图片失败")
                return False
                
        except Exception as e:
            print(f"  渲染过程出错: {e}")
            return False

def analyze_image_dimensions(image_path):
    """分析图片尺寸和布局"""
    try:
        img = mpimg.imread(image_path)
        
        # 转换为灰度图
        if len(img.shape) == 3:
            gray = np.mean(img, axis=2)
        else:
            gray = img
        
        # 找到非白色像素
        non_white = gray < 0.9
        
        # 找到内容的边界框
        rows = np.any(non_white, axis=1)
        cols = np.any(non_white, axis=0)
        
        if not np.any(rows) or not np.any(cols):
            return None
        
        rmin, rmax = np.where(rows)[0][[0, -1]]
        cmin, cmax = np.where(cols)[0][[0, -1]]
        
        height = rmax - rmin + 1
        width = cmax - cmin + 1
        aspect_ratio = width / height
        
        return {
            "height": height,
            "width": width,
            "aspect_ratio": aspect_ratio,
            "total_pixels": height * width
        }
        
    except Exception as e:
        print(f"分析图片失败: {e}")
        return None

def comprehensive_analysis():
    """综合分析"""
    print("=== 深度分析LaTeX分数渲染问题 ===\n")
    
    # 1. 创建参考的垂直分数
    print("1. 创建参考垂直分数...")
    ref_success = create_reference_fraction()
    print(f"参考分数创建: {'✅ 成功' if ref_success else '❌ 失败'}")
    
    # 2. 测试不同文档类
    print("\n2. 测试不同文档类配置...")
    results = test_different_document_classes()
    
    # 3. 分析所有图片
    print("\n3. 分析所有生成的图片...")
    all_images = ["reference_vertical.png"] + [r["filename"] for r in results.values() if r["success"]]
    
    analysis_results = {}
    for img_file in all_images:
        if Path(img_file).exists():
            dimensions = analyze_image_dimensions(img_file)
            analysis_results[img_file] = dimensions
            
            if dimensions:
                print(f"{img_file}:")
                print(f"  尺寸: {dimensions['height']}x{dimensions['width']}")
                print(f"  宽高比: {dimensions['aspect_ratio']:.2f}")
                
                if dimensions['aspect_ratio'] > 2.0:
                    print(f"  ⚠️  水平分数")
                elif dimensions['aspect_ratio'] < 1.5:
                    print(f"  ✅ 垂直分数")
                else:
                    print(f"  ❓ 中等比例")
            else:
                print(f"{img_file}: 分析失败")
    
    # 4. 寻找真正的垂直分数
    print("\n4. 寻找真正的垂直分数...")
    vertical_fractions = []
    for img_file, dimensions in analysis_results.items():
        if dimensions and dimensions['aspect_ratio'] < 1.5:
            vertical_fractions.append(img_file)
    
    if vertical_fractions:
        print(f"找到 {len(vertical_fractions)} 个垂直分数:")
        for vf in vertical_fractions:
            print(f"  ✅ {vf}")
    else:
        print("❌ 没有找到真正的垂直分数！")
    
    return analysis_results

def create_final_comparison(analysis_results):
    """创建最终对比图"""
    valid_images = [(k, v) for k, v in analysis_results.items() if v is not None and Path(k).exists()]
    
    if not valid_images:
        print("没有有效的图片进行对比")
        return
    
    # 按宽高比排序
    valid_images.sort(key=lambda x: x[1]['aspect_ratio'])
    
    n_images = len(valid_images)
    cols = 3
    rows = (n_images + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
    if rows == 1 and cols == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes.reshape(1, -1)
    elif cols == 1:
        axes = axes.reshape(-1, 1)
    else:
        axes = axes.flatten()
    
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    for i, (img_file, dimensions) in enumerate(valid_images):
        try:
            img = mpimg.imread(img_file)
            axes[i].imshow(img)
            
            title = img_file.replace('.png', '').replace('_', ' ')
            title += f"\n宽高比: {dimensions['aspect_ratio']:.2f}"
            
            # 根据宽高比设置颜色
            if dimensions['aspect_ratio'] > 2.0:
                color = 'red'
                weight = 'bold'
            elif dimensions['aspect_ratio'] < 1.5:
                color = 'green'
                weight = 'bold'
            else:
                color = 'orange'
                weight = 'normal'
            
            axes[i].set_title(title, fontsize=10, color=color, weight=weight)
            axes[i].axis('off')
            
        except Exception as e:
            axes[i].text(0.5, 0.5, f'加载失败\n{e}', 
                        ha='center', va='center', transform=axes[i].transAxes)
            axes[i].set_title(img_file, fontsize=10)
            axes[i].axis('off')
    
    # 隐藏多余的子图
    for i in range(len(valid_images), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('LaTeX分数渲染深度分析\n按宽高比排序 - 红色=水平分数, 绿色=垂直分数', fontsize=14)
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    # 执行综合分析
    analysis_results = comprehensive_analysis()
    
    # 创建最终对比
    print("\n创建最终对比图...")
    create_final_comparison(analysis_results)

if __name__ == "__main__":
    main()
