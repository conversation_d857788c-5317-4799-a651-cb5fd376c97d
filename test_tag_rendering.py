#!/usr/bin/env python3
"""
测试\tag命令的实际渲染效果
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_tag_rendering():
    """测试\tag命令的实际渲染效果"""
    print("=== 测试\\tag命令实际渲染效果 ===")
    
    # 测试用例
    test_cases = [
        {
            "name": "简单tag测试",
            "latex": "a+b=c \\tag{2}",
            "expected": "应该显示公式 a+b=c 右侧有编号 (2)"
        },
        {
            "name": "数字tag",
            "latex": "E = mc^2 \\tag{1}",
            "expected": "应该显示公式右侧有编号 (1)"
        },
        {
            "name": "文字tag",
            "latex": "F = ma \\tag{牛顿定律}",
            "expected": "应该显示公式右侧有标签 (牛顿定律)"
        },
        {
            "name": "复杂公式tag",
            "latex": "\\frac{dy}{dx} = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h} \\tag{导数}",
            "expected": "应该显示分数公式右侧有标签 (导数)"
        }
    ]
    
    try:
        from render_image import render_latex_to_image
        
        print("\n🧪 开始渲染测试...")
        
        results = {}
        
        for i, test_case in enumerate(test_cases, 1):
            name = test_case["name"]
            latex = test_case["latex"]
            expected = test_case["expected"]
            
            print(f"\n测试 {i}: {name}")
            print(f"LaTeX: {latex}")
            print(f"期望: {expected}")
            
            output_file = f"tag_render_test_{i}_{name.replace(' ', '_')}.png"
            
            try:
                success = render_latex_to_image(latex, output_file)
                
                if success:
                    print(f"  ✅ 渲染成功: {output_file}")
                    print(f"  📁 请检查图片是否显示了正确的标签/编号")
                    results[name] = "成功"
                else:
                    print(f"  ❌ 渲染失败")
                    results[name] = "失败"
                    
            except Exception as e:
                print(f"  ❌ 渲染出错: {str(e)[:100]}...")
                results[name] = f"错误: {str(e)[:50]}..."
        
        return results
        
    except ImportError as e:
        print(f"❌ 无法导入render_image模块: {e}")
        return {}

def check_processing_logic():
    """检查LaTeX处理逻辑"""
    print("\n=== 检查LaTeX处理逻辑 ===")
    
    try:
        from render_image import _process_latex_string
        
        test_latex = "a+b=c \\tag{2}"
        
        print(f"原始LaTeX: {test_latex}")
        
        processed = _process_latex_string(test_latex)
        print(f"处理后LaTeX: {processed}")
        
        # 检查处理结果
        if '\\tag{2}' in processed:
            print("✅ \\tag命令保留完整")
        else:
            print("❌ \\tag命令可能被修改或丢失")
        
        if 'gather}' in processed and 'gather*}' not in processed:
            print("✅ 使用了支持\\tag的gather环境")
        elif 'gather*}' in processed:
            print("❌ 使用了不支持\\tag的gather*环境")
        else:
            print("⚠️  使用了其他环境")
        
        return processed
        
    except Exception as e:
        print(f"❌ 处理逻辑检查失败: {e}")
        return None

def manual_latex_test():
    """手动LaTeX测试"""
    print("\n=== 手动LaTeX测试 ===")
    
    # 创建一个简单的LaTeX文件进行测试
    test_latex_content = r"""
\documentclass[24pt,border=1pt]{standalone}
\usepackage{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\begin{document}
\fontsize{24}{29}\selectfont
\begin{gather}
\displaystyle a+b=c \tag{2}
\end{gather}
\end{document}
"""
    
    print("测试LaTeX内容:")
    print(test_latex_content)
    
    # 保存到文件
    test_file = "manual_tag_test.tex"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_latex_content)
    
    print(f"\n✅ LaTeX文件已保存: {test_file}")
    print("💡 您可以手动编译此文件来验证\\tag是否正常工作")
    print("编译命令: xelatex manual_tag_test.tex")

def main():
    """主函数"""
    print("LaTeX \\tag命令渲染效果测试")
    print("=" * 50)
    
    print("\n🎯 测试目标:")
    print("  验证\\tag命令是否能正确渲染出公式编号/标签")
    print("  特别测试 'a+b=c \\tag{2}' 是否显示 (2)")
    
    # 检查处理逻辑
    processed = check_processing_logic()
    
    # 测试实际渲染
    results = test_tag_rendering()
    
    # 创建手动测试文件
    manual_latex_test()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("🎯 测试结果总结")
    print("=" * 50)
    
    if results:
        success_count = sum(1 for result in results.values() if result == "成功")
        total_count = len(results)
        
        print(f"\n📊 渲染测试结果:")
        print(f"  总测试数: {total_count}")
        print(f"  成功数: {success_count}")
        print(f"  成功率: {success_count/total_count*100:.1f}%")
        
        for name, result in results.items():
            status = "✅" if result == "成功" else "❌"
            print(f"  {status} {name}: {result}")
        
        if success_count == total_count:
            print(f"\n🎉 所有渲染测试通过！")
            print(f"请检查生成的图片文件，确认\\tag编号是否正确显示")
        else:
            print(f"\n⚠️  部分渲染测试失败")
            print(f"可能需要进一步调试\\tag渲染问题")
    
    print(f"\n💡 验证步骤:")
    print(f"  1. 检查生成的PNG文件")
    print(f"  2. 确认公式右侧是否显示了正确的编号/标签")
    print(f"  3. 特别检查 'a+b=c \\tag{{2}}' 是否显示 (2)")
    print(f"  4. 如果编号不显示，说明需要进一步修复")

if __name__ == "__main__":
    main()
