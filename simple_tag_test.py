#!/usr/bin/env python3
"""
简单的\tag命令测试
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_simple_tag():
    """测试简单的\tag命令"""
    print("=== 简单\\tag命令测试 ===")
    
    try:
        from render_image import render_latex_to_image
        
        # 最简单的测试
        test_latex = "a+b=c \\tag{2}"
        output_file = "simple_tag_result.png"
        
        print(f"测试LaTeX: {test_latex}")
        print("期望: 显示 a+b=c 公式，右侧有编号 (2)")
        
        success = render_latex_to_image(test_latex, output_file)
        
        if success:
            print(f"✅ 渲染成功: {output_file}")
            
            # 检查文件
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"📊 文件大小: {file_size} 字节")
                
                if file_size > 500:
                    print("✅ 文件大小合理，应该包含渲染内容")
                    return True
                else:
                    print("⚠️  文件大小很小，可能渲染有问题")
                    return False
            else:
                print("❌ 输出文件不存在")
                return False
        else:
            print("❌ 渲染失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_minimal_latex():
    """创建最小的LaTeX测试文件"""
    print("\n=== 创建最小LaTeX测试 ===")
    
    # 最简单的LaTeX文件
    minimal_latex = r"""
\documentclass{article}
\usepackage{amsmath}
\begin{document}
\begin{gather}
a+b=c \tag{2}
\end{gather}
\end{document}
"""
    
    test_file = "minimal_tag_test.tex"
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(minimal_latex)
    
    print(f"✅ 最小测试文件已创建: {test_file}")
    print("💡 编译命令: xelatex minimal_tag_test.tex")
    
    return test_file

def check_output_files():
    """检查输出文件"""
    print("\n=== 检查输出文件 ===")
    
    files_to_check = [
        "simple_tag_result.png",
        "specific_tag_test.png",
        "tag_render_test_1_简单tag测试.png"
    ]
    
    for filename in files_to_check:
        if Path(filename).exists():
            file_size = Path(filename).stat().st_size
            print(f"📁 {filename}: {file_size} 字节")
            
            if file_size > 1000:
                print(f"  ✅ 文件大小正常")
            else:
                print(f"  ⚠️  文件大小较小")
        else:
            print(f"❌ {filename}: 文件不存在")

def main():
    """主函数"""
    print("简单\\tag命令测试")
    print("=" * 40)
    
    print("\n🎯 测试目标:")
    print("  验证 'a+b=c \\tag{2}' 是否能正确渲染")
    print("  检查是否显示了 (2) 编号")
    
    # 运行简单测试
    success = test_simple_tag()
    
    # 创建最小测试文件
    minimal_file = create_minimal_latex()
    
    # 检查输出文件
    check_output_files()
    
    # 总结
    print("\n" + "=" * 40)
    print("🎯 测试总结")
    print("=" * 40)
    
    if success:
        print("✅ 基本渲染测试通过")
        print("\n📋 验证步骤:")
        print("  1. 查看生成的PNG文件")
        print("  2. 确认是否显示了公式和编号")
        print("  3. 如果看到 a+b=c (2)，说明\\tag工作正常")
        
        print("\n💡 如果编号不显示:")
        print("  这可能是LaTeX环境的问题，而不是我们代码的问题")
        print("  可以尝试编译最小测试文件来验证LaTeX环境")
    else:
        print("❌ 基本渲染测试失败")
        print("需要进一步调试")
    
    print(f"\n🔧 调试建议:")
    print(f"  1. 手动编译: xelatex {minimal_file}")
    print(f"  2. 检查是否生成了PDF文件")
    print(f"  3. 如果PDF正常显示 a+b=c (2)，说明LaTeX环境正常")
    print(f"  4. 如果PDF也没有编号，说明是LaTeX配置问题")

if __name__ == "__main__":
    main()
