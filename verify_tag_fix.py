#!/usr/bin/env python3
"""
验证\tag命令修复效果
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_tag_processing():
    """测试\tag命令的处理逻辑"""
    print("=== 测试\\tag命令处理逻辑 ===")
    
    try:
        from render_image import _process_latex_string
        
        test_cases = [
            "a+b=c \\tag{2}",
            "E = mc^2 \\tag{Einstein}",
            "x + y = z \\tag{简单}",
            "\\frac{a}{b} \\tag{分数}",  # 这个有分数，应该也用gather
        ]
        
        for i, latex in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {latex}")
            
            try:
                processed = _process_latex_string(latex)
                print(f"  处理后: {processed}")
                
                # 检查是否使用了正确的环境
                if '\\tag' in latex:
                    if 'gather}' in processed and 'gather*}' not in processed:
                        print(f"  ✅ 正确使用gather环境（支持\\tag）")
                    elif 'gather*}' in processed:
                        print(f"  ❌ 错误使用gather*环境（不支持\\tag）")
                    elif processed.startswith('$') and processed.endswith('$'):
                        print(f"  ❌ 错误使用内联数学模式（不支持\\tag）")
                    else:
                        print(f"  ⚠️  使用其他环境: {processed[:50]}...")
                
            except Exception as e:
                print(f"  ❌ 处理出错: {e}")
        
    except ImportError:
        print("无法导入处理函数")

def test_specific_case():
    """测试具体的问题案例"""
    print("\n=== 测试具体问题案例 ===")
    
    test_latex = "a+b=c \\tag{2}"
    
    try:
        from render_image import render_latex_to_image
        
        print(f"测试LaTeX: {test_latex}")
        print("期望结果: 显示 a+b=c 公式，右侧有编号 (2)")
        
        output_file = "specific_tag_test.png"
        
        success = render_latex_to_image(test_latex, output_file)
        
        if success:
            print(f"✅ 渲染成功: {output_file}")
            print("📁 请检查图片是否显示了 (2) 编号")
            
            # 检查文件大小
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"📊 文件大小: {file_size} 字节")
                
                if file_size > 1000:  # 合理的文件大小
                    print("✅ 文件大小正常，可能包含内容")
                else:
                    print("⚠️  文件大小较小，可能渲染有问题")
            
            return True
        else:
            print("❌ 渲染失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_manual_test():
    """创建手动测试文件"""
    print("\n=== 创建手动测试文件 ===")
    
    # 创建一个完整的LaTeX文件用于手动验证
    latex_content = r"""
\documentclass[24pt,border=1pt]{standalone}
\usepackage{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\begin{document}
\fontsize{24}{29}\selectfont
\begin{gather}
\displaystyle a+b=c \tag{2}
\end{gather}
\end{document}
"""
    
    test_file = "manual_tag_verification.tex"
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(latex_content)
    
    print(f"✅ 手动测试文件已创建: {test_file}")
    print("💡 编译命令: xelatex manual_tag_verification.tex")
    print("📋 期望结果: 应该生成显示 a+b=c (2) 的PDF文件")

def main():
    """主函数"""
    print("LaTeX \\tag命令修复验证")
    print("=" * 50)
    
    print("\n🎯 验证目标:")
    print("  确认 'a+b=c \\tag{2}' 能正确渲染出 (2) 编号")
    print("  验证修复后的处理逻辑是否正确")
    
    print("\n🔧 修复内容:")
    print("  添加了特殊处理：即使没有其他数学命令，")
    print("  只要包含\\tag就使用gather环境")
    
    # 测试处理逻辑
    test_tag_processing()
    
    # 测试具体案例
    success = test_specific_case()
    
    # 创建手动测试
    create_manual_test()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 验证结果")
    print("=" * 50)
    
    if success:
        print("✅ 渲染测试通过")
        print("\n📋 验证步骤:")
        print("  1. 查看生成的 specific_tag_test.png")
        print("  2. 确认是否显示了 a+b=c 公式")
        print("  3. 确认右侧是否有 (2) 编号")
        print("  4. 如果编号显示正确，说明修复成功")
        
        print("\n💡 如果仍然没有显示编号:")
        print("  1. 可能是LaTeX编译器的问题")
        print("  2. 可能需要检查字体或显示设置")
        print("  3. 可以尝试手动编译 manual_tag_verification.tex")
    else:
        print("❌ 渲染测试失败")
        print("需要进一步调试问题")
    
    print("\n🔍 调试提示:")
    print("  如果问题仍然存在，请检查:")
    print("  1. xelatex是否正确安装")
    print("  2. amsmath宏包是否可用")
    print("  3. 是否有其他LaTeX环境问题")

if __name__ == "__main__":
    main()
