from texteller import load_model, load_tokenizer, img2latex
import os
from tqdm import tqdm

# Load models
model = load_model(use_onnx=False)
tokenizer = load_tokenizer()

# # Convert image to LaTeX
# latex = img2latex(model, tokenizer, ["/aipdf-mlp/guofu/datasets/Formula/test_imgs/0002.png"])[0]
# print(latex[2:-2])

root_path = r"/aipdf-mlp/guofu/datasets/MSR/test_imgs1"
img_list = os.listdir(root_path)
img_list.sort()
out_text = []
cnt = 1
for imname in tqdm(img_list):
    latex = img2latex(model, tokenizer, [os.path.join(root_path, imname)])[0]
    print(cnt, latex)
    cnt += 1
    out_text.append(latex)
with open('pred_texteller_1.txt', 'w') as f:
    for text in out_text:
        f.write(text + '\n')
