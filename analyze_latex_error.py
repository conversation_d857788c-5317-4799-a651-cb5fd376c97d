#!/usr/bin/env python3
"""
分析LaTeX语法错误
"""

def analyze_latex_string():
    """分析有问题的LaTeX字符串"""
    print("=== LaTeX语法错误分析 ===")
    
    # 用户提供的LaTeX字符串
    problematic_latex = r"\[b_{j}*u \left \{ \begin{matrix} = \sum_{i}b_{ij}q_{i} \left( T-1 \right)&& \if& \lambda_{j} \left( t \right)>0, \\ \leqq \sum_{i}b_{ij}q_{i} \left( T-1 \right)&& \if& \lambda_{j} \left( T \right)=0; \end{matrix} \right.\]"
    
    print("🔍 问题LaTeX字符串:")
    print(problematic_latex)
    print()
    
    print("🚨 错误分析:")
    print("错误信息: Incomplete \\ifx; all text was ignored after line 11.")
    print("错误原因: LaTeX中的 \\if 是条件命令，需要配对的 \\fi")
    print()
    
    print("🔍 问题定位:")
    print("在字符串中找到了两个 \\if:")
    
    # 查找\if的位置
    import re
    if_positions = []
    for match in re.finditer(r'\\if', problematic_latex):
        start = match.start()
        end = match.end()
        context = problematic_latex[max(0, start-10):min(len(problematic_latex), end+10)]
        if_positions.append((start, context))
        print(f"  位置 {start}: ...{context}...")
    
    print()
    print("💡 问题解释:")
    print("在数学公式中，\\if 是LaTeX的条件命令，但这里应该是想表示 'if' 这个词")
    print("正确的做法是使用 \\text{if} 或 \\mathrm{if}")
    
    print()
    print("🔧 修复方案:")
    
    # 方案1：使用\text{if}
    fixed_latex_1 = problematic_latex.replace(r'&& \if&', r'&& \text{if}&')
    print("方案1 - 使用 \\text{if}:")
    print(fixed_latex_1)
    print()
    
    # 方案2：使用\mathrm{if}
    fixed_latex_2 = problematic_latex.replace(r'&& \if&', r'&& \mathrm{if}&')
    print("方案2 - 使用 \\mathrm{if}:")
    print(fixed_latex_2)
    print()
    
    # 方案3：使用\text{if }（带空格）
    fixed_latex_3 = problematic_latex.replace(r'&& \if&', r'&& \text{if }&')
    print("方案3 - 使用 \\text{if }（带空格）:")
    print(fixed_latex_3)
    print()
    
    return [fixed_latex_1, fixed_latex_2, fixed_latex_3]

def test_fixes():
    """测试修复方案"""
    print("=== 测试修复方案 ===")
    
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))
    
    try:
        from render_image import render_latex_to_image
        
        fixes = analyze_latex_string()
        
        for i, fixed_latex in enumerate(fixes, 1):
            print(f"\n测试方案 {i}:")
            output_file = f"test_fix_{i}.png"
            
            try:
                success = render_latex_to_image(fixed_latex, output_file)
                if success:
                    print(f"  ✅ 渲染成功: {output_file}")
                else:
                    print(f"  ❌ 渲染失败")
            except Exception as e:
                print(f"  ❌ 渲染出错: {str(e)[:100]}...")
    
    except ImportError:
        print("无法导入render_image模块，跳过渲染测试")
        analyze_latex_string()

def main():
    """主函数"""
    print("LaTeX语法错误分析和修复")
    print("=" * 50)
    
    print("\n🎯 问题总结:")
    print("  错误类型: LaTeX语法错误")
    print("  错误位置: \\if 命令")
    print("  错误原因: \\if 是LaTeX条件命令，需要配对的 \\fi")
    print("  解决方案: 将 \\if 改为 \\text{if} 或 \\mathrm{if}")
    
    print("\n🔧 开始分析和修复...")
    test_fixes()
    
    print("\n💡 建议:")
    print("  1. 推荐使用 \\text{if} - 最常用的文本表示方法")
    print("  2. 或使用 \\mathrm{if} - 数学模式下的正体文本")
    print("  3. 检查其他可能的LaTeX关键字冲突")
    
    print("\n📝 修复步骤:")
    print("  1. 在您的LaTeX字符串中找到所有的 \\if")
    print("  2. 将它们替换为 \\text{if}")
    print("  3. 重新运行渲染程序")

if __name__ == "__main__":
    main()
