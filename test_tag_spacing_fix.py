#!/usr/bin/env python3
"""
测试\tag间距修复
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

def test_tag_spacing_fix():
    """测试\tag间距修复"""
    print("=== 测试\\tag间距修复 ===")
    
    try:
        from render_image import render_latex_to_image
        
        test_latex = "a+b=c \\tag{2}"
        
        print(f"测试LaTeX: {test_latex}")
        print("修复内容: 裁剪时为\\tag保留更多水平空间")
        
        # 测试修复后的效果
        output_file = "tag_spacing_fixed.png"
        
        print(f"\n🧪 开始渲染测试...")
        success = render_latex_to_image(test_latex, output_file)
        
        if success:
            print(f"✅ 渲染成功: {output_file}")
            print("📋 请检查图片:")
            print("  • 公式 a+b=c 应该在左侧")
            print("  • 标签 (2) 应该在右侧，与公式有明显间距")
            print("  • 不应该是紧贴的 a+b=c(2) 效果")
            
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"📊 文件大小: {file_size} 字节")
                return True
            else:
                print("❌ 输出文件不存在")
                return False
        else:
            print("❌ 渲染失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_comparison():
    """测试对比效果"""
    print("\n=== 测试对比效果 ===")
    
    try:
        from render_image import render_latex_to_image
        
        # 测试普通公式（无\tag）
        normal_latex = "a+b=c"
        print(f"对比测试1: {normal_latex}")
        
        success1 = render_latex_to_image(normal_latex, "normal_formula.png")
        
        if success1:
            print(f"✅ 普通公式渲染成功: normal_formula.png")
        else:
            print(f"❌ 普通公式渲染失败")
        
        # 测试带\tag的公式
        tag_latex = "a+b=c \\tag{2}"
        print(f"对比测试2: {tag_latex}")
        
        success2 = render_latex_to_image(tag_latex, "tag_formula.png")
        
        if success2:
            print(f"✅ 带标签公式渲染成功: tag_formula.png")
        else:
            print(f"❌ 带标签公式渲染失败")
        
        return success1, success2
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        return False, False

def main():
    """主函数"""
    print("LaTeX \\tag间距修复测试")
    print("=" * 50)
    
    print("\n🔍 问题分析:")
    print("  裁剪逻辑移除了公式和\\tag标签之间的空白")
    print("  导致标签看起来紧贴在公式后面")
    
    print("\n🔧 修复方案:")
    print("  1. 修改_crop_to_content函数，添加preserve_tag_spacing参数")
    print("  2. 检测\\tag命令，为其保留更多水平空间")
    print("  3. 水平边距从5像素增加到20像素")
    
    # 测试修复效果
    success = test_tag_spacing_fix()
    
    # 测试对比
    success1, success2 = test_comparison()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果")
    print("=" * 50)
    
    if success:
        print("✅ 主要修复测试通过")
    else:
        print("❌ 主要修复测试失败")
    
    if success1 and success2:
        print("✅ 对比测试通过")
        print("\n📋 生成的文件:")
        print("  • tag_spacing_fixed.png - 修复后的\\tag效果")
        print("  • normal_formula.png - 普通公式对比")
        print("  • tag_formula.png - 带标签公式")
        
        print("\n💡 验证方法:")
        print("  查看tag_spacing_fixed.png和tag_formula.png")
        print("  确认标签(2)是否与公式a+b=c有明显分离")
    else:
        print("❌ 对比测试失败")
    
    print(f"\n🎯 期望效果:")
    print(f"  修复后应该显示:")
    print(f"    a + b = c        (2)")
    print(f"    ↑公式          ↑标签（有间距）")
    print(f"  而不是:")
    print(f"    a + b = c(2)")
    print(f"    ↑紧贴在一起")

if __name__ == "__main__":
    main()
