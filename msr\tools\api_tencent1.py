#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯云 OCR 公式识别（GeneralBasicOCR）
"""

import json, base64, sys, time, hashlib, hmac, urllib.request
# ======= 需修改的 4 处 =======
SECRET_ID  = "AKID0sa6fATdbP1oRvGRP9qNiyDNVpyz2zmM"
SECRET_KEY = "TkqnbGj185tWwGVZP2e7NRM8lCTYP3Um"
REGION     = "ap-beijing"                # 区域
SERVICE    = "ocr"
# =============================

HOST = f"{SERVICE}.tencentcloudapi.com"
ENDPOINT = f"https://{HOST}/"

def sign(key, msg):
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()

def get_signature(payload: str) -> str:
    algorithm = "TC3-HMAC-SHA256"
    timestamp = str(int(time.time()))
    date = time.strftime("%Y-%m-%d", time.gmtime(int(timestamp)))

    # 1. canonical request
    http_method = "POST"
    canonical_uri = "/"
    canonical_querystring = ""
    ct = "application/json; charset=utf-8"
    canonical_headers = f"content-type:{ct}\nhost:{HOST}\n"
    signed_headers = "content-type;host"
    hashed_payload = hashlib.sha256(payload.encode()).hexdigest()
    canonical_request = f"{http_method}\n{canonical_uri}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{hashed_payload}"

    # 2. string to sign
    credential_scope = f"{date}/{SERVICE}/tc3_request"
    hashed_canon = hashlib.sha256(canonical_request.encode()).hexdigest()
    string_to_sign = f"{algorithm}\n{timestamp}\n{credential_scope}\n{hashed_canon}"

    # 3. signature
    secret_date = sign(("TC3" + SECRET_KEY).encode(), date)
    secret_service = sign(secret_date, SERVICE)
    secret_signing = sign(secret_service, "tc3_request")
    signature = sign(secret_signing, string_to_sign).hex()

    authorization = (f"{algorithm} Credential={SECRET_ID}/{credential_scope}, "
                     f"SignedHeaders={signed_headers}, Signature={signature}")
    return authorization, timestamp

def ocr_formula(path: str) -> dict:
    with open(path, "rb") as f:
        img_b64 = base64.b64encode(f.read()).decode()

    payload = json.dumps({"ImageBase64": img_b64}, ensure_ascii=False)
    auth, ts = get_signature(payload)

    headers = {
        "Authorization": auth,
        "Content-Type": "application/json; charset=utf-8",
        "Host": HOST,
        "X-TC-Action": "GeneralBasicOCR",
        "X-TC-Version": "2018-11-19",
        "X-TC-Region": REGION,
        "X-TC-Timestamp": ts
    }

    req = urllib.request.Request(
        ENDPOINT,
        data=payload.encode(),
        headers=headers
    )
    resp = urllib.request.urlopen(req, timeout=30)
    return json.loads(resp.read().decode())

if __name__ == "__main__":
    # if len(sys.argv) != 2:
    #     print("用法: python tencent_formula.py image.png")
    #     sys.exit(1)

    # js = ocr_formula(sys.argv[1])

    img_path = r"D:\datasets\MSR\formula_set_select2\DLF_000003_01.png"
    js = ocr_formula(img_path)
    print(json.dumps(js, ensure_ascii=False, indent=2))