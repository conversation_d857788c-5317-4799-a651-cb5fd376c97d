#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为云通用 OCR 公式识别示例（Token 认证）
"""

import json, base64, sys, urllib.request, urllib.error

# ======= 1. 需填的 4 个字段 =======
DOMAIN      = "ocr.cn-north-4.myhuaweicloud.com"      # 区域域名
PROJECT_ID  = "YOUR_PROJECT_ID"                       # 项目 ID
AK          = "YOUR_AK"
SK          = "YOUR_SK"
# ==================================

TOKEN_URL = f"https://iam.{DOMAIN.split('.',1)[1]}/v3/auth/tokens"
OCR_URL   = f"https://{DOMAIN}/v2/{PROJECT_ID}/ocr/general-formula"

def get_token() -> str:
    """用 AK/SK 换取 IAM Token"""
    body = {
        "auth": {
            "identity": {
                "methods": ["aksk"],
                "aksk": {
                    "access_key": AK,
                    "secret_key": SK
                }
            },
            "scope": {
                "project": { "name": DOMAIN.split(".")[0] }  # 如 cn-north-4
            }
        }
    }
    req = urllib.request.Request(
        TOKEN_URL,
        data=json.dumps(body).encode(),
        headers={"Content-Type": "application/json"}
    )
    resp = urllib.request.urlopen(req)
    return resp.headers["X-Subject-Token"]

def ocr_formula(image_path: str) -> dict:
    """调公式接口 -> dict"""
    with open(image_path, "rb") as f:
        img_b64 = base64.b64encode(f.read()).decode()

    body = json.dumps({"image": img_b64, "formula": True})
    req = urllib.request.Request(
        OCR_URL,
        data=body.encode(),
        headers={
            "Content-Type": "application/json",
            "X-Auth-Token": get_token()
        }
    )
    resp = urllib.request.urlopen(req, timeout=30)
    return json.loads(resp.read().decode())

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python hw_formula.py image.png")
        sys.exit(1)

    js = ocr_formula(sys.argv[1])
    print(json.dumps(js, ensure_ascii=False, indent=2))