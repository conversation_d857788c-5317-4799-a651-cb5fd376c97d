#!/usr/bin/env python3
"""
分析生成的图片，检查分数是否为水平还是垂直显示
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import numpy as np
import os
from pathlib import Path

def analyze_fraction_layout(image_path):
    """分析分数的布局（水平vs垂直）"""
    try:
        img = mpimg.imread(image_path)
        
        # 转换为灰度图
        if len(img.shape) == 3:
            gray = np.mean(img, axis=2)
        else:
            gray = img
        
        # 找到非白色像素（假设背景是白色）
        non_white = gray < 0.9  # 阈值可能需要调整
        
        # 找到内容的边界框
        rows = np.any(non_white, axis=1)
        cols = np.any(non_white, axis=0)
        
        if not np.any(rows) or not np.any(cols):
            return "无内容", img.shape
        
        rmin, rmax = np.where(rows)[0][[0, -1]]
        cmin, cmax = np.where(cols)[0][[0, -1]]
        
        height = rmax - rmin + 1
        width = cmax - cmin + 1
        
        # 计算宽高比
        aspect_ratio = width / height
        
        # 分析布局特征
        if aspect_ratio > 2.0:
            layout_type = "水平布局（可能是水平分数）"
        elif aspect_ratio < 0.8:
            layout_type = "垂直布局（可能是垂直分数）"
        else:
            layout_type = "方形布局（需要进一步分析）"
        
        return layout_type, (height, width, aspect_ratio)
        
    except Exception as e:
        return f"分析失败: {e}", None

def detailed_fraction_analysis():
    """详细分析所有生成的分数图片"""
    
    # 要分析的图片文件
    image_files = [
        "standard_config.png",
        "no_xfrac.png", 
        "inline_mode.png",
        "displaystyle.png",
        "dfrac.png",
        "tfrac.png"
    ]
    
    print("=== 分数布局分析报告 ===\n")
    
    results = {}
    
    for img_file in image_files:
        if os.path.exists(img_file):
            print(f"分析 {img_file}:")
            layout_type, dimensions = analyze_fraction_layout(img_file)
            print(f"  布局类型: {layout_type}")
            
            if dimensions and len(dimensions) == 3:
                height, width, aspect_ratio = dimensions
                print(f"  尺寸: {height}x{width} (高x宽)")
                print(f"  宽高比: {aspect_ratio:.2f}")
                
                # 判断是否为问题中的水平分数
                if aspect_ratio > 2.0:
                    print(f"  ⚠️  疑似水平分数显示问题！")
                elif aspect_ratio < 1.2:
                    print(f"  ✅ 正常的垂直分数显示")
                else:
                    print(f"  ❓ 需要人工确认")
            
            results[img_file] = (layout_type, dimensions)
            print()
        else:
            print(f"❌ 文件不存在: {img_file}")
            results[img_file] = ("文件不存在", None)
    
    return results

def create_visual_report(results):
    """创建可视化报告"""
    existing_files = [(k, v) for k, v in results.items() if v[1] is not None and os.path.exists(k)]
    
    if not existing_files:
        print("没有可用的图片文件进行可视化")
        return
    
    # 创建子图显示
    n_files = len(existing_files)
    cols = 3
    rows = (n_files + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
    if rows == 1 and cols == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes.reshape(1, -1)
    elif cols == 1:
        axes = axes.reshape(-1, 1)
    else:
        axes = axes.flatten()
    
    # 解决中文显示问题
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    for i, (img_file, (layout_type, dimensions)) in enumerate(existing_files):
        try:
            img = mpimg.imread(img_file)
            axes[i].imshow(img)
            
            # 创建标题
            title = img_file.replace('.png', '').replace('_', ' ')
            if dimensions and len(dimensions) == 3:
                height, width, aspect_ratio = dimensions
                title += f"\n宽高比: {aspect_ratio:.2f}"
                
                # 根据宽高比添加颜色标记
                if aspect_ratio > 2.0:
                    axes[i].set_title(title, fontsize=10, color='red', weight='bold')
                elif aspect_ratio < 1.2:
                    axes[i].set_title(title, fontsize=10, color='green')
                else:
                    axes[i].set_title(title, fontsize=10, color='orange')
            else:
                axes[i].set_title(title, fontsize=10)
            
            axes[i].axis('off')
            
        except Exception as e:
            axes[i].text(0.5, 0.5, f'加载失败\n{e}', 
                        ha='center', va='center', transform=axes[i].transAxes)
            axes[i].set_title(img_file, fontsize=10)
            axes[i].axis('off')
    
    # 隐藏多余的子图
    for i in range(len(existing_files), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('LaTeX分数渲染分析\n红色=疑似水平分数, 绿色=正常垂直分数, 橙色=需确认', fontsize=14)
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    print("开始分析LaTeX分数渲染结果...")
    
    # 详细分析
    results = detailed_fraction_analysis()
    
    # 创建可视化报告
    print("创建可视化报告...")
    create_visual_report(results)
    
    # 总结分析
    print("=== 总结 ===")
    horizontal_count = 0
    vertical_count = 0
    
    for img_file, (layout_type, dimensions) in results.items():
        if dimensions and len(dimensions) == 3:
            _, _, aspect_ratio = dimensions
            if aspect_ratio > 2.0:
                horizontal_count += 1
                print(f"⚠️  {img_file}: 疑似水平分数问题")
            elif aspect_ratio < 1.2:
                vertical_count += 1
                print(f"✅ {img_file}: 正常垂直分数")
    
    print(f"\n发现 {horizontal_count} 个疑似水平分数问题")
    print(f"发现 {vertical_count} 个正常垂直分数")

if __name__ == "__main__":
    main()
