#!/usr/bin/env python3
import sys, os
sys.path.append(os.path.join(os.path.dirname(__file__), 'msr', 'tools'))

try:
    from render_image import render_latex_to_image
    print("测试修复后的\\tag间距")
    print("LaTeX: a+b=c \\tag{2}")
    
    success = render_latex_to_image("a+b=c \\tag{2}", "fixed_tag_spacing.png")
    
    if success:
        print("✅ 渲染成功: fixed_tag_spacing.png")
        print("📋 请检查图片中标签(2)是否与公式有适当间距")
        print("💡 修复内容: 为\\tag保留了更多水平空间(20像素边距)")
    else:
        print("❌ 渲染失败")
        
except Exception as e:
    print(f"错误: {e}")
